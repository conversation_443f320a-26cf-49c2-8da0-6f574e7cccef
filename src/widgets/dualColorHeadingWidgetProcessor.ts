import { WidgetUtils } from '../utils/widgetUtils';
import { TextNode } from '../types/figma';
import { Utils } from '../utils/utils';
import { FrameNode } from '../types/figma';

export class DualColorHeadingWidgetProcessor {
  static process(node: TextNode, data: any): any {
    if (!data.settings) {
      data.settings = {};
    }

    data.settings.eael_show_dch_icon_content = ""; // static - no icon
    data.settings.eael_dch_last_title = ""; // static - no last title
    data.settings.eael_dch_subtext = ""; // static - no subtext

    // Process content alignment from parent node
    this.processContentAlignment(node, data);

    if (node.type === "TEXT") {
      Utils.processTitleNode(node, data, 'eael_dch_first_title', 'eael-dch-first-title');
    } else {
      // multiple header
      const textNodes = Utils.findTextNodes(node as unknown as FrameNode);
      if (textNodes.length > 0) {
        data.settings.eael_dch_enable_multiple_titles = "yes";

        data.settings.eael_dch_multiple_titles = [];

        textNodes.forEach((textNode) => {
          const repeaterItem = Utils.processRepeaterTitleNode(textNode, data, `eael_dch_title`, `eael-dch-title`);
          data.settings.eael_dch_multiple_titles.push(repeaterItem);
        });
      }
    }

    WidgetUtils.processPadding(node, data, "eael-dch-container");
    WidgetUtils.processMargin(node, data, "eael-dch-container");
    WidgetUtils.processBackground(node, data, "eael-dch-container");

    return data;
  }

  private static processContentAlignment(node: any, data: any): void {
    // Default alignment
    let contentAlignment = "left";

    // Check parent node for alignment properties
    const parentNode = node.parent;
    if (parentNode && "layoutMode" in parentNode) {
      let alignmentItems: any;
      const layoutMode = parentNode.layoutMode;

      // Get alignment based on layout mode
      if (layoutMode === "VERTICAL" && "counterAxisAlignItems" in parentNode) {
        alignmentItems = parentNode.counterAxisAlignItems;
      } else if (layoutMode === "HORIZONTAL" && "primaryAxisAlignItems" in parentNode) {
        alignmentItems = parentNode.primaryAxisAlignItems;
      }

      // Convert Figma alignment to Elementor alignment values
      if (alignmentItems === "CENTER") {
        contentAlignment = "center";
      } else if (alignmentItems === "MAX") {
        contentAlignment = "right";
      } else if (alignmentItems === "MIN") {
        contentAlignment = "left";
      }
    }

    // Set the content alignment
    data.settings.eael_dch_content_alignment = contentAlignment;
  }
}