import { Utils } from '../utils/utils';
import { WidgetUtils } from '../utils/widgetUtils';
import { ColorUtils } from '../utils/colorUtils';
import { ElementData, SceneNode, TextNode } from '../types/figma';

export class TestimonialWidgetProcessor {

  public static process(node: SceneNode, data: ElementData): void {


    try {
      // Initialize settings if not exists
      if (!data.settings) {
        data.settings = {};
      }

      // Set widget type and default settings
      data.settings.eael_testimonial_style = 'content-bottom-icon-title-inline';
      data.settings.eael_testimonial_user_display_block = 'yes';

      // Add new control settings with default values
      data.settings.eael_testimonial_show_quote = ''; // Default to showing quote
      // data.settings.eael_testimonial_padding = {
      //   unit: "px",
      //   top: "20",
      //   right: "20",
      //   bottom: "20",
      //   left: "20",
      //   isLinked: true
      // };

      // Find all necessary nodes
      const nameNode = Utils.findNodeRecursively(node, undefined, 'name');
      const companyNode = Utils.findNodeRecursively(node, undefined, 'company');
      const contentNode = Utils.findNodeRecursively(node, undefined, 'content');
      const imageNode = Utils.findNodeRecursively(node, undefined, 'image');
      const ratingNode = Utils.findNodeRecursively(node, undefined, 'rating');

      // Process name
      if (nameNode) {
        Utils.processTitleNode(nameNode, data, 'eael_testimonial_name', 'eael-testimonial-name');
      }

      // Process position/company
      if (companyNode) {
        let textNode: TextNode | null = null;

        if (companyNode.type === 'TEXT') {
          textNode = companyNode as TextNode;
        } else if ('children' in companyNode && companyNode.children) {
          const foundNode = companyNode.children.find(child => child.type === 'TEXT');
          if (foundNode && foundNode.type === 'TEXT') {
            textNode = foundNode as TextNode;
          }
        }

        if (textNode && textNode.characters) {
          data.settings.eael_testimonial_company_title = textNode.characters;
          WidgetUtils.processTypographySettings(textNode, data, 'eael-testimonial-company');
          WidgetUtils.processTextColor(textNode, data, 'eael-testimonial-company');
        }
      }

      // Process content/description
      if (contentNode) {
        let textNode: TextNode | null = null;

        if (contentNode.type === 'TEXT') {
          textNode = contentNode as TextNode;
        } else if ('children' in contentNode && contentNode.children) {
          const foundNode = contentNode.children.find(child => child.type === 'TEXT');
          if (foundNode && foundNode.type === 'TEXT') {
            textNode = foundNode as TextNode;
          }
        }

        if (textNode && textNode.characters) {
          data.settings.eael_testimonial_description = textNode.characters;
          WidgetUtils.processTypographySettings(textNode, data, 'eael-testimonial-content');
          WidgetUtils.processTextColor(textNode, data, 'eael-testimonial-content');

          // Initialize margin object
          const margin = {
            unit: "px",
            top: "0",
            right: "0",
            bottom: "0",
            left: "0",
            isLinked: false
          };

          // Calculate spacing between content and avatar/name
          if (imageNode || nameNode) {
            const targetNode = imageNode || nameNode;
            const spacing = WidgetUtils.calculateSpacingBetweenNodes(targetNode, contentNode);
            if (spacing) {
              margin.bottom = spacing.toString();
            }
          }

          // Process review/rating spacing
          if (ratingNode) {
            const ratingPosition = WidgetUtils.determineRelativePosition(ratingNode, contentNode);
            data.settings.eael_testimonial_rating_position = ratingPosition;

            const spacing = WidgetUtils.calculateSpacingBetweenNodes(ratingNode, contentNode);
            if (spacing) {
              if (ratingPosition === 'top') {
                margin.top = spacing.toString();
              } else {
                margin.bottom = spacing.toString();
              }
            }
          }

          // Set the final margin
          data.settings.eael_testimonial_description_margin = margin;
        }
      }

      // Process image
      if (imageNode) {
        data.settings.eael_testimonial_image_rounded = 'testimonial-avatar-rounded';
        data.settings.eael_testimonial_image_width = {
          unit: 'px',
          size: imageNode.width,
          sizes: []
        };
        data.settings.eael_testimonial_max_image_width = {
          unit: '%',
          size: imageNode.width,
          sizes: []
        };
        WidgetUtils.processMargin(imageNode, data, 'eael-testimonial-image');

        // process image
        if (global.ImageArray && global.ImageArray[imageNode.id]) {
          data.settings.image = {
            url: global.ImageArray[imageNode.id],
            id: '',
            size: '',
            alt: imageNode.name || ''
          };
        }
      }

      // Process rating (simplified - handles both review and star functionality)
      if (ratingNode) {
        let starNode = ratingNode; // Default to the rating node itself
        let colorNode = ratingNode; // Default to the rating node itself

        // If rating node is a frame, look for star elements inside it
        if (ratingNode.type === 'FRAME' && 'children' in ratingNode && ratingNode.children) {
          // Look for star-like child nodes (VECTOR, STAR, or nodes with "star" in name)
          const starChild = ratingNode.children.find(child =>
            child.type === 'VECTOR' ||
            child.type === 'STAR' ||
            child.name.toLowerCase().includes('star')
          );

          if (starChild) {
            starNode = starChild;
            colorNode = starChild;
          }
        }

        // Process color for the rating/star elements
        if (colorNode.type === 'TEXT') {
          WidgetUtils.processTextColor(colorNode, data, 'eael-testimonial-rating');
        } else if ('fills' in colorNode && colorNode.fills && colorNode.fills.length > 0) {
          // Handle VECTOR, STAR, or other nodes with fills
          const fill = colorNode.fills[0];
          if (fill.type === 'SOLID') {
            const color = ColorUtils.rgbToHex(fill.color);
            if (color) {
              data.settings.eael_testimonial_rating_item_color = color;
            }
          }
        }

        // Set rating item size based on the star node dimensions
        let ratingSize = 16; // Default size
        if ('width' in starNode && starNode.width) {
          ratingSize = starNode.width;
        } else if (starNode.type === 'TEXT' && 'fontSize' in starNode && starNode.fontSize) {
          ratingSize = starNode.fontSize;
        }

        data.settings.eael_testimonial_rating_item_size = {
          unit: 'px',
          size: ratingSize,
          sizes: []
        };

        // Process margin for the rating container (use the original rating node for spacing)
        WidgetUtils.processMargin(ratingNode, data, 'eael-testimonial-rating');
      }

      // Process container background
      WidgetUtils.processBackground(node, data, 'eael-testimonial-container');
      WidgetUtils.processPadding(node, data, 'eael-testimonial-container');

      // Set static settings
      data.settings.eael_testimonial_quotation_color = '';
      data.settings.eael_testimonial_quotation_top = {
        unit: '%',
        size: '',
        sizes: []
      };
      data.settings.eael_testimonial_quotation_right = {
        unit: '%',
        size: '',
        sizes: []
      };

    } catch (error) {
      throw error;
    }
  }
}