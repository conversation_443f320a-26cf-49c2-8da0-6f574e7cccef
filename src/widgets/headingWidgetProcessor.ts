import { Utils } from '../utils/utils';
import { ElementData, SceneNode, FrameNode, TextNode } from '../types/figma';

export class HeadingWidgetProcessor {
  public static process(node: SceneNode, data: ElementData): void {


    // Set default header size
    data.settings.header_size = "h2";

    // Handle both TEXT nodes and FRAME nodes that contain text
    if (node.type === "TEXT") {

      // Direct text node - use processTitleNode for consistency
      Utils.processTitleNode(node, data, 'title', 'heading-title');

      // Process parent alignment for TEXT nodes
      if (node.parent) {
        this.processAlignment(node.parent, data);
      }
    } else if (node.type === "FRAME") {

      // Frame node - find text child and process it
      const frameNode = node as FrameNode;

      // Try to find a text node child
      let textNode: TextNode | undefined;
      if (frameNode.children) {
        textNode = frameNode.children.find(child => child.type === "TEXT") as TextNode;
      }

      if (textNode) {

        // Use processTitleNode to handle the text content and styling
        Utils.processTitleNode(textNode, data, 'title', 'heading-title');

        // For frame nodes, process alignment of the frame itself
        this.processAlignment(frameNode, data);
      } else {

        // Fallback: set empty title
        data.settings.title = "";
      }
    } else {

      // Fallback for other node types
      data.settings.title = node.name || "";
    }

    // Process height
    if (node.height) {
      data.settings.height = "min-height";
      data.settings.custom_height = {
        unit: "px",
        size: node.height,
        sizes: []
      };
    }
  }

  private static processAlignment(targetNode: any, data: ElementData): void {
    // Generic alignment processing for any node with layout properties
    if (!targetNode || !("layoutMode" in targetNode)) {
      return;
    }

    let alignmentItems: any;
    const layoutMode = targetNode.layoutMode;

    if ((layoutMode === "VERTICAL" || layoutMode === "HORIZONTAL") && "counterAxisAlignItems" in targetNode) {
      alignmentItems = targetNode.counterAxisAlignItems;
    }

    const alignment = alignmentItems === "MAX"
      ? "flex-end"
      : alignmentItems === "CENTER"
        ? "center"
        : alignmentItems === "MIN"
          ? "flex-start"
          : "";

    // Set alignment
    if (alignment) {
      data.settings._flex_align_self = alignment;
    }
  }
}