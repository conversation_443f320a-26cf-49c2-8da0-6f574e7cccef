import { WidgetUtils } from '../utils/widgetUtils';
import { Utils } from '../utils/utils';
import { ElementData } from '../types/figma';
// import { ImageArray } from '../code';

export class InfoBoxWidgetProcessor {
  public static process(node: any, data: ElementData) {

    // Find container node regardless of naming convention (widget- prefix or not)
    const containerNode = node; // Use the main node as container by default

    // Try to find child elements by explicit naming first
    let subtitleNode = Utils.findNodeRecursively(node, undefined, "subtitle");
    let titleNode = Utils.findNodeRecursively(node, undefined, "title");
    let contentNode = Utils.findNodeRecursively(node, undefined, "content");
    let buttonNode = Utils.findNodeRecursively(node, undefined, "button");
    let mainContentNode = Utils.findNodeRecursively(node, undefined, "main-content");
    let imageNode = Utils.findNodeRecursively(node, undefined, "image");
    let iconNode = Utils.findNodeRecursively(node, undefined, "icon");



    // If main content node not found, try to detect it automatically
    if (!mainContentNode && node.children && node.children.length > 0) {
      // Look for a frame that contains title, content, and possibly button
      for (const child of node.children) {
        if (child.type === 'FRAME' && child.children && child.children.length >= 2) {
          // If this frame has multiple text elements, it's likely the main content
          const textNodes = Utils.findTextNodes(child);
          if (textNodes.length >= 2) {
            mainContentNode = child;

            // If we found main content but not other elements, try to detect them within it
            if (!titleNode && textNodes.length > 0) {
              // First text node with larger font size is likely the title
              const possibleTitle = textNodes.find(text =>
                text.fontSize && text.fontSize >= 16 && text.characters.length < 50
              );
              if (possibleTitle) {
                titleNode = possibleTitle;
              }
            }

            if (!contentNode && textNodes.length > 1) {
              // Text node with more content is likely the content
              const possibleContent = textNodes.find(text =>
                text !== titleNode && text.characters.length > 50
              );
              if (possibleContent) {
                contentNode = possibleContent;
              }
            }

            break;
          }
        }
      }
    }

    // If image not found, try to detect it automatically
    if (!imageNode && node.children) {
      // Look for rectangle with image fill
      for (const child of node.children) {
        if ((child.type === 'RECTANGLE' || child.type === 'FRAME') &&
            child.fills && Array.isArray(child.fills) &&
            child.fills.some((fill: any) => fill.type === 'IMAGE')) {
          imageNode = child;
          break;
        }
      }
    }

    // If button not found, try to detect it automatically
    if (!buttonNode && node.children) {
      // Look for a small frame with background fill and text that could be a button
      for (const child of node.children) {
        if (child.type === 'FRAME' && child.fills &&
            child.fills.length > 0 && child.width < 200 &&
            child.height < 80) {
          const buttonText = Utils.findTextNodes(child);
          if (buttonText.length > 0 && buttonText[0].characters.length < 30) {
            buttonNode = child;
            break;
          }
        }
      }
    }

    if (!Array.isArray(data.settings)) {
      data.settings.eael_show_infobox_clickable = ""; // default for now

      if (mainContentNode) {
        // process margin of children and inner children
        WidgetUtils.processMargin(mainContentNode, data, "eael-info-box-main-content");
      }

      if (imageNode) {
        data.settings.eael_infobox_img_or_icon = "img";
        const imageParentNode = Utils.findParentNode(node, imageNode) as any;
        const imageParentNodeChildren = imageParentNode?.children;
        const imageIndex = imageParentNodeChildren?.indexOf(imageNode);
        const mainContentNodeIndex = imageParentNodeChildren?.indexOf(mainContentNode);
        let imagePosition = "";

        if (imageIndex >= 0 && mainContentNodeIndex >= 0) {
          const flexDirection = (imageParentNode?.layoutMode) === "HORIZONTAL" ? "row" : "column";
          imagePosition =
            imageIndex < mainContentNodeIndex
              ? flexDirection === "row"
                ? "left"
                : "top"
              : flexDirection === "row"
                ? "right"
                : "bottom";

          let mainContentAlignment = "left";
          if (mainContentNode && "layoutMode" in mainContentNode) {
            let mainContentLayoutMode = mainContentNode.layoutMode;
            let mainContentAlignmentItems: any;

            if (mainContentLayoutMode === "VERTICAL" &&
              "counterAxisAlignItems" in mainContentNode) {
              mainContentAlignmentItems = mainContentNode.counterAxisAlignItems;
            }
            else if (mainContentLayoutMode === "HORIZONTAL" &&
              "primaryAxisAlignItems" in mainContentNode) {
              mainContentAlignmentItems = mainContentNode.primaryAxisAlignItems;
            }

            mainContentAlignment =
              mainContentAlignmentItems === "MAX"
                ? "right"
                : mainContentAlignmentItems === "CENTER"
                  ? "center"
                  : mainContentAlignment;
          }

          let imageParentVerticalAlignment = "middle";
          if (imageParentNode && "layoutMode" in imageParentNode) {
            let imageParentLayoutMode = imageParentNode.layoutMode;
            let imageParentAlignmentItems: any;

            if (imageParentLayoutMode === "VERTICAL" &&
              "counterAxisAlignItems" in imageParentNode) {
              imageParentAlignmentItems = imageParentNode.counterAxisAlignItems;
            }
            else if (imageParentLayoutMode === "HORIZONTAL" &&
              "primaryAxisAlignItems" in imageParentNode) {
              imageParentAlignmentItems = imageParentNode.primaryAxisAlignItems;
            }

            imageParentVerticalAlignment =
              imageParentAlignmentItems === "MAX"
                ? "bottom"
                : imageParentAlignmentItems === "CENTER"
                  ? "middle"
                  : imageParentAlignmentItems === "MIN"
                    ? "top"
                    : imageParentVerticalAlignment;
          }

          if (imagePosition === "right") {
            data.settings.eael_infobox_content_alignment = "left";
            data.settings.eael_infobox_img_type = "img-on-right";
          }
          else if (imagePosition === "left") {
            data.settings.eael_infobox_content_alignment = mainContentAlignment;
            data.settings.eael_infobox_img_type = "img-on-left";
          }
          else if (imagePosition === "top") {
            data.settings.eael_infobox_content_alignment = mainContentAlignment; // content left, center, right
            data.settings.eael_infobox_img_type = "img-on-top";
          }
          else if (imagePosition === "bottom") {
            data.settings.eael_infobox_content_alignment = mainContentAlignment;
            data.settings.eael_infobox_img_type = "img-on-bottom";
          }

          data.settings.icon_vertical_position = imageParentVerticalAlignment;
        }

        // image alignment from constraints
        if ("constraints" in imageNode) {
          const constraints = imageNode.constraints as any;
          if (constraints.vertical === "MIN") {
            data.settings.icon_vertical_position = "top";
          }
          else if (constraints.vertical === "MAX") {
            data.settings.icon_vertical_position = "bottom";
          }
          else if (constraints.vertical === "CENTER") {
            data.settings.icon_vertical_position = "middle";
          }
        }

        // image size
        const imageSize = imageNode.width;
        data.settings.eael_infobox_image_resizer = {
          unit: "px",
          size: imageSize,
          sizes: [],
        };

        // image url
        // Check if the image node has fills
        if ("fills" in imageNode &&
          Array.isArray(imageNode.fills) &&
          imageNode.fills.length > 0) {
          const imageFill = imageNode.fills.find((fill) => fill.type === "IMAGE");
          if (imageFill && global.ImageArray[imageNode.id]) {
            // We don't need imageHash for now as we're using the global ImageArray
            data.settings.eael_infobox_image = {
              // url: `https://api.figma.com/v1/images/${imageFill.imageHash}`, // not working. we will revamp later.
              // id: "",
              // size: "",
              // alt: "",
              // source: "figma", // not working. we will revamp later
              url: global.ImageArray[imageNode.id],
              size: "",
              alt: "",
              source: "figma",
            };
          }
        }

        WidgetUtils.processBorderRadius(imageNode, data, "eael-info-box-image");
        if (imagePosition !== "right") {
          WidgetUtils.processMargin(imageNode, data, "eael-info-box-image");
        }
      }

      if (iconNode && !imageNode) {
        // if content node is sibling of icon node, then it is icon instead of image. we dont consider icon from main-content frame
        const iconParentNode = Utils.findParentNode(node, iconNode) as any;
        const iconParentNodeChildren = iconParentNode?.children;
        const mainContentNodeIndex = iconParentNodeChildren?.indexOf(mainContentNode);

        if (mainContentNodeIndex >= 0) {
          data.settings.eael_infobox_img_or_icon = "icon";
        }
      }

      if (containerNode) {
        WidgetUtils.processBackground(containerNode, data, "eael-info-box-container");
        WidgetUtils.processPadding(containerNode, data, "eael-info-box-container");
      }


      Utils.processTitleNode(titleNode, data, "eael_infobox_title", "eael-info-box-title");

      // Subtitle
      let args: any = {};
      args.titleTagKey = 'eael_infobox_sub_title_tag';
      args.titleTag = 'h4';

      args.showTitleKey = 'eael_infobox_show_sub_title';
      args.showTitle = 'yes';

      Utils.processTitleNode(subtitleNode, data, "eael_infobox_sub_title", "eael-info-box-subtitle", args);

      if (contentNode &&
        ("children" in contentNode || contentNode.type === "TEXT")) {
        let textItems = [];

        if (contentNode.type === "TEXT") {
          if ("characters" in contentNode) {
            textItems.push(`<p>${contentNode.characters}</p>`);
          }

          WidgetUtils.processTypographySettings(contentNode, data, "eael-info-box-content");
          WidgetUtils.processTextColor(contentNode, data, "eael-info-box-content");
          WidgetUtils.processMargin(contentNode, data, "eael-info-box-content");
        }
        else {
          contentNode.children.forEach((frame) => {
            const frameNode = frame; // Ensure it's treated as a SceneNode

            if (frameNode.type === "FRAME") {
              // Find all TEXT nodes, including in inner children
              const textNodes = Utils.findTextNodes(frameNode);

              if (textNodes.length > 0) {
                textNodes.forEach((textNode) => {
                  // CRITICAL: Skip text from title and subtitle nodes
                  const parentNode = Utils.findParentNode(contentNode, textNode);
                  if (parentNode && (parentNode === titleNode || parentNode === subtitleNode)) {
                    return; // Skip this text node
                  }

                  if ("characters" in textNode) {
                    textItems.push(`<p>${textNode.characters}</p>`);
                  }
                });

                // Only process styling if we have valid content text
                if (textItems.length > 0) {
                  WidgetUtils.processTypographySettings(textNodes[0], data, "eael-info-box-content");
                  WidgetUtils.processTextColor(textNodes[0], data, "eael-info-box-content");
                }
              }
            }
          });
        }

        // Generate the final HTML list
        data.settings.eael_infobox_text = `${textItems.join("")}`;
      }

      // Button processing
      if (buttonNode) {
        // Ensure data.settings is an object, not an array
        const textNodes = Utils.findTextNodes(buttonNode as any);
        let buttonText = []; // unnecessary array; it can be string;

        if (textNodes && textNodes.length > 0) {
          textNodes.forEach((textNode) => {
            if ("characters" in textNode) {
              buttonText.push(`${textNode.characters}`);
            }
          });

          data.settings.infobox_button_text = buttonText.join(" "); // Combine all text nodes into one string
          data.settings.eael_show_infobox_button = "yes";

          WidgetUtils.processTypographySettings(textNodes[0], data, "eael-info-box-button");
          WidgetUtils.processTextColor(textNodes[0], data, "eael-info-box-button");
          WidgetUtils.processBackground(buttonNode, data, "eael-info-box-button");
          WidgetUtils.processPadding(buttonNode, data, "eael-info-box-button");
          WidgetUtils.processBorderRadius(buttonNode, data, "eael-info-box-button");
          WidgetUtils.processBorderWidth(buttonNode, data, "eael-info-box-button");
          // WidgetUtils.processMargin(buttonNode, data, 'eael-info-box-button');
        }

        // get icon node within button starts with icon-
        const buttonIconNode = (buttonNode as any).children?.find((child: any) => child.name.startsWith('icon-'));

        if (buttonIconNode) {
          Utils.processIconNode(buttonIconNode, data, 'eael_infobox_button_icon_new', 'eael_infobox_button_icon_rotate');

          // icon size
          const iconSize = buttonIconNode.width;
          data.settings.eael_infobox_button_icon_size = {
            unit: "px",
            size: iconSize,
            sizes: [],
          };

          // icon indent
          const buttonGap = "itemSpacing" in buttonNode ? buttonNode.itemSpacing : "";
          if (buttonGap) {
            data.settings.eael_infobox_button_icon_indent = {
              unit: "px",
              size: buttonGap,
              sizes: [],
            };
          }

          // icon alignment
          // button node has two child. text and icon. if text is first child then icon on the right
          const iconIndex = (buttonNode as any).children?.indexOf(buttonIconNode);
          const textIndex = (buttonNode as any).children?.indexOf(textNodes[0]);

          if (iconIndex >= 0 && textIndex >= 0) {
            data.settings.eael_infobox_button_icon_alignment =
              iconIndex > textIndex ? "right" : "left";
          }
        }
      }
    }
  }
}