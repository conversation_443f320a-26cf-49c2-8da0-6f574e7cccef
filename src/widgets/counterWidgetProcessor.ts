import { WidgetUtils } from '../utils/widgetUtils';
import { Utils } from '../utils/utils';
import { ColorUtils } from '../utils/colorUtils';
import { ElementData, SceneNode, FrameNode, TextNode } from '../types/figma';

export class CounterWidgetProcessor {
  public static process(node: FrameNode, data: ElementData): void {
    // Find container node regardless of naming convention (widget- prefix or not)
    const containerNode = node; // Use the main node as container by default
    const numberNode = Utils.findNodeRecursively(node, undefined, "number");
    const titleNode = Utils.findNodeRecursively(node, undefined, "title");
    const iconNode = Utils.findNodeRecursively(node, undefined, "icon");

    // Process container settings
    if (containerNode) {
      WidgetUtils.processBorderRadius(containerNode, data, "eael-counter-container");
      WidgetUtils.processBackground(containerNode, data, "eael-counter-container");
      WidgetUtils.processPadding(containerNode, data, "eael-counter-container");
    }

    // Process number node
    if (numberNode) {
      let textNode = (numberNode as FrameNode).children?.find(child => child.type === "TEXT") as SceneNode & { characters?: string };

      if (!textNode) {
        textNode = numberNode.type === "TEXT" ? numberNode : textNode;
      }

      if (textNode && !Array.isArray(data.settings)) {
        let ending_number = Utils.getTextWithCase(textNode);
        let ending_number_raw = ending_number;
        // Take number only from the text (it may contain plus icon)
        data.settings.ending_number = ending_number.replace(/\D/g, "");
        // Take non-number as suffix or prefix
        data.settings.number_suffix = ending_number_raw.replace(/\d/g, ""); // suffix for now
        if (textNode.type === "TEXT") {
          WidgetUtils.processTypographySettings(textNode as TextNode, data, "eael-counter-number");
          WidgetUtils.processTypographySettings(textNode as TextNode, data, "eael-counter-suffix"); // Using same for suffix
          WidgetUtils.processTextColor(textNode as TextNode, data, "eael-counter-number"); // Using same for suffix
        }
      }
    }

    // Process title
    Utils.processTitleNode(titleNode, data, 'counter_title', 'eael-counter-title');

    // Process icon
    if (iconNode) {
      Utils.processIconNode(iconNode, data, 'counter_icon_new', 'counter_icon_rotation');

      let iconWidth = iconNode.width;
      if (!Array.isArray(data.settings)) {
        data.settings.counter_icon_color = ""; // static for now
        data.settings.counter_layout = "layout-6"; // static for now
        data.settings.eael_icon_type = "icon"; // static for now
        data.settings.counter_icon_size = {
          unit: "px",
          size: iconWidth,
          sizes: []
        };
      }

      if ("parent" in iconNode) {
        WidgetUtils.processMargin(iconNode, data, "eael-counter-icon");
      }
      WidgetUtils.processPadding(iconNode, data, "eael-counter-icon");
      WidgetUtils.processBorderRadius(iconNode, data, "eael-counter-icon");
    }

    // Initialize settings if not present
    if (!data.settings) {
      data.settings = {};
    }

    // Find all text nodes
    const textNodes = Utils.findTextNodes(node);

    // Set default text alignment
    data.settings.textAlign = "LEFT";
    data.settings.textDecoration = "NONE";
    data.settings.textCase = "ORIGINAL";

    // Process background color
    if (node.fills?.[0]?.type === 'SOLID') {
      data.settings._background_color = ColorUtils.rgbToHex(node.fills[0].color);
    } else {
      data.settings._background_color = "#ffffff";
    }

    // Process padding
    if (node.paddingLeft || node.paddingRight || node.paddingTop || node.paddingBottom) {
      data.settings.padding = {
        unit: "px",
        top: node.paddingTop?.toString() || "16",
        right: node.paddingRight?.toString() || "16",
        bottom: node.paddingBottom?.toString() || "16",
        left: node.paddingLeft?.toString() || "16",
        isLinked: true
      };
    } else {
      data.settings.padding = {
        unit: "px",
        top: "16",
        right: "16",
        bottom: "16",
        left: "16",
        isLinked: true
      };
    }

    // Process title (second text node if exists)
    if (textNodes.length > 1) {
      const titleNode = textNodes[1];
      data.settings.counter_title = Utils.getTextWithCase(titleNode) || '';

      // Process title typography and color
      WidgetUtils.processTypographySettings(titleNode, data, "eael-counter-title");
      WidgetUtils.processTextColor(titleNode, data, "eael-counter-title");
    }

    // Set default margin
    data.settings.margin = {
      unit: "px",
      top: "0",
      right: "0",
      bottom: "16",
      left: "0",
      isLinked: false
    };
  }

  private static splitNumberAndSuffix(text: string): [string, string] {
    // Split the text into number and suffix
    const match = text.match(/^(\d+)(.*)$/);
    if (match) {
      return [match[1], match[2]];
    }
    return [text, ''];
  }
}