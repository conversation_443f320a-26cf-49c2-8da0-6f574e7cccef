import { Utils } from '../utils/utils';
import { WidgetUtils } from '../utils/widgetUtils';

export class PostCarouselWidgetProcessor {
  public static process(node: any, data: any) {
    if (!Array.isArray(data.settings)) {
      const sectionTitleNode = Utils.findNodeRecursively(node, undefined, 'section-title', 'eael-post-carousel');
      const titleNode = Utils.findNodeRecursively(node, undefined, 'title', 'eael-post-carousel');
      const excerptNode = Utils.findNodeRecursively(node, undefined, 'content', 'eael-post-carousel');
      const metaNode = Utils.findNodeRecursively(node, undefined, 'meta', 'eael-post-carousel');
      const readMoreNode = Utils.findNodeRecursively(node, undefined, 'read-more', 'eael-post-carousel');
      const dotNode = Utils.findNodeRecursively(node, undefined, 'dot', 'eael-post-carousel');
      const activeDotNode = Utils.findNodeRecursively(node, undefined, 'active-dot', 'eael-post-carousel');

      data.settings.arrows = ''; // no carousel arrows by default

      if (sectionTitleNode) {
        Utils.processTitleNode(sectionTitleNode, data, 'eael_post_carousel_title', 'eael-post-carousel-section-title'); // eael_post_carousel_title for title text. and other styles prefix: eael_carousel_title_[style]
      }

      // bg from widget node or container
      WidgetUtils.processBackground(node, data, 'eael-post-carousel-container');

      if (titleNode) {
        Utils.processTitleNode(titleNode, data, 'eael_post_grid_title', 'eael-post-carousel-title');
      }

      if (excerptNode) {
        // data.settings.show_excerpt = 'yes';
        // data.settings.excerpt_length = 15;
        Utils.processTitleNode(excerptNode, data, 'eael_post_grid_excerpt', 'eael-post-carousel-content');
      }

      if (metaNode) {
        // data.settings.show_meta = 'yes';
        // data.settings.meta_data = ['author', 'date', 'comments'];
      }

      if (readMoreNode) {
        // data.settings.show_read_more = 'yes';
        // data.settings.read_more_text = 'Read More';
      }

      if (dotNode || activeDotNode) {
        data.settings.show_navigation = 'yes';
        data.settings.navigation_position = 'center';
        data.settings.navigation_style = 'dots';
        data.settings.show_pagination = 'yes';
        data.settings.pagination_position = 'center';
      }

      if (dotNode) {
        // Set default carousel settings
        data.settings.carousel_autoplay = 'yes';
        data.settings.carousel_autoplay_speed = 3000;
        data.settings.carousel_loop = 'yes';
        data.settings.carousel_pause_on_hover = 'yes';

        // Process dot styling
        WidgetUtils.processBackground(dotNode, data, 'eael-post-carousel-dot');
        WidgetUtils.processBorderRadius(dotNode, data, 'eael-post-carousel-dot');

        // Set dot size if available
        if (dotNode.width && dotNode.height) {
          // not needed but plugin json includes this too
          data.settings.eael_post_grid_pagination_dot_size = {
            unit: 'px',
            size: Math.min(dotNode.width, dotNode.height),
            sizes: []
          };

          // correct
          data.settings.dots_size = data.settings.eael_post_grid_pagination_dot_size;
        }
      }

      if (activeDotNode) {
        // Set default carousel settings (if not already set by dotNode)
        if (!data.settings.carousel_autoplay) {
          data.settings.carousel_autoplay = 'yes';
          data.settings.carousel_autoplay_speed = 3000;
          data.settings.carousel_loop = 'yes';
          data.settings.carousel_pause_on_hover = 'yes';
        }

        // Process active dot styling
        WidgetUtils.processBackground(activeDotNode, data, 'eael-post-carousel-active-dot');
        WidgetUtils.processBorderRadius(activeDotNode, data, 'eael-post-carousel-active-dot');

        // Set active dot size if available
        if (activeDotNode.width && activeDotNode.height) {
          data.settings.eael_post_grid_pagination_dot_active_size = {
            unit: 'px',
            size: Math.min(activeDotNode.width, activeDotNode.height),
            sizes: []
          };

          data.settings.active_dots_height = data.settings.eael_post_grid_pagination_dot_active_size;
          data.settings.active_dots_width = data.settings.eael_post_grid_pagination_dot_active_size;

        }
      }
    }
  }
}