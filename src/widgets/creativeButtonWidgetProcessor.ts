import { ColorUtils } from '../utils/colorUtils';
import { ElementData, SceneNode, FrameNode, TextNode } from '../types/figma';
import { WidgetUtils } from '../utils/widgetUtils';
import { Utils } from '../utils/utils';

export class CreativeButtonWidgetProcessor {
  public static process(node: FrameNode, data: ElementData): void {
    let textNode: TextNode | undefined;
    let iconNode: SceneNode | undefined;
    if ("children" in node && Array.isArray(node.children)) {
      textNode = node.children?.find(child => child.type === "TEXT") as TextNode;
      iconNode = node.children?.find(child => child.name.startsWith('icon-'));
    }
    
    Utils.processTitleNode(textNode, data, 'creative_button_text', 'eael-creative-button-text');

    Utils.processIconNode(iconNode, data, 'eael_creative_button_icon_new', 'eael_creative_button_icon_rotate');

    if (!Array.isArray(data.settings)) {
      data.settings.eael_creative_button_icon_alignment =
        "children" in node &&
          node.children &&
          iconNode &&
          textNode &&
          node.children.length >= 2 &&
          node.children[0].type === "TEXT" &&
          node.children[1].name.startsWith("icon-")
          ? "right"
          : "left";
      if (iconNode && textNode) {
        // get indent from node gap
        const buttonGap = "itemSpacing" in node ? node.itemSpacing : "";
        if (buttonGap) {
          data.settings.eael_creative_button_icon_indent = {
            unit: "px",
            size: buttonGap,
            sizes: [],
          };
        }
        // icon size from icon node width
        const iconSize = iconNode.width;
        data.settings.eael_creative_button_icon_size = {
          unit: "px",
          size: iconSize,
          sizes: [],
        };
      }
    }
    WidgetUtils.processBackground(node, data, "eael-creative-button-container");
    WidgetUtils.processBorderRadius(node, data, "eael-creative-button-container");
  }
} 