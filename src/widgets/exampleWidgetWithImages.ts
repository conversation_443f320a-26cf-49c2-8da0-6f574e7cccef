import { ElementData } from '../types/figma';

export class ExampleWidgetWithImages {
  public static process(node: any, data: ElementData) {
    // You can access the global ImageArray directly
    if (!Array.isArray(data.settings) && node.id && global.ImageArray && global.ImageArray[node.id]) {
      // Example: Set the background image URL from the global ImageArray
      data.settings = {
        ...data.settings,
        background_image: {
          url: global.ImageArray[node.id],
          id: '',
          size: 'cover',
          position: 'center center',
          repeat: 'no-repeat',
          attachment: 'scroll'
        }
      };


    }

    // Process child nodes that might have images
    if ('children' in node && Array.isArray(node.children)) {
      node.children.forEach((childNode: any) => {
        if (childNode.id && global.ImageArray && global.ImageArray[childNode.id]) {
          // Example: Add the child image to a gallery
          if (!Array.isArray(data.settings)) {
            if (!data.settings.gallery) {
              data.settings.gallery = [];
            }

            data.settings.gallery.push({
              url: global.ImageArray[childNode.id],
              id: '',
              size: '',
              alt: childNode.name || ''
            });
          }
        }
      });
    }
  }
}
