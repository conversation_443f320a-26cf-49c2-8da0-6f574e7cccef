import { Utils } from '../utils/utils';
import { SceneNode, TextNode } from '../types/figma';
import { WidgetUtils } from '../utils/widgetUtils';

export class ButtonWidgetProcessor {
  public static process(node: any, data: any) {
    let textNode: TextNode | undefined;
    let iconNode: SceneNode | undefined;

    if ("children" in node && Array.isArray(node.children)) {
      textNode = node.children?.find((child: SceneNode) => child.type === "TEXT");
      iconNode = node.children?.find((child: SceneNode) => child.name.startsWith('icon-'));
    }

    Utils.processTitleNode(textNode, data, 'text', 'button-text');
    Utils.processIconNode(iconNode, data, 'selected_icon');

    data.settings.icon_align =
      "children" in node &&
        node.children &&
        node.children.length >= 2 &&
        node.children[0].type === "TEXT" &&
        node.children[1].name.startsWith("icon-")
        ? "row-reverse"
        : "row";

    const buttonParentNode = node.parent;
    let buttonParentAlignmentItems;
    let buttonParentAlignment = "";
    if (buttonParentNode && "layoutMode" in buttonParentNode) {
      let buttonParentLayoutMode = buttonParentNode.layoutMode;
      if (buttonParentLayoutMode === "VERTICAL" &&
        "counterAxisAlignItems" in buttonParentNode) {
        buttonParentAlignmentItems = buttonParentNode.counterAxisAlignItems;
      }
      else if (buttonParentLayoutMode === "HORIZONTAL" &&
        "counterAxisAlignItems" in buttonParentNode) {
        buttonParentAlignmentItems = buttonParentNode.counterAxisAlignItems;
      }
    }

    buttonParentAlignment =
      buttonParentAlignmentItems === "MAX"
        ? "flex-end"
        : buttonParentAlignmentItems === "CENTER"
          ? "center"
          : buttonParentAlignmentItems === "MIN"
            ? "flex-start"
            : buttonParentAlignment;

    if (buttonParentAlignment) {
      data.settings._flex_align_self = buttonParentAlignment;
    }

    // process button background
    WidgetUtils.processBackground(node, data, 'button-container');
  }
} 