import { WidgetUtils } from '../utils/widgetUtils';
import { Utils } from '../utils/utils';
import { ElementData, FrameNode } from '../types/figma';
import { displayNameMap } from '../config/widgetSettings';

export class CtaBoxWidgetProcessor {
  public static process(node: FrameNode, data: ElementData): void {
    if (!Array.isArray(data.settings)) {
      // Find elements using simple node name checks like Info Box widget
      const titleNode = Utils.findNodeRecursively(node, undefined, "title");
      const contentNode = Utils.findNodeRecursively(node, undefined, "content");

      // Find all buttons recursively
      const allButtons = this.findAllButtons(node);
      const buttonNode = allButtons.length > 0 ? allButtons[0] : null;
      const secondaryButtonNode = allButtons.length > 1 ? allButtons[1] : null;



      // default settings
      data.settings.eael_cta_content = '';

      // Process title - handle both single and multiple titles
      if (titleNode) {

        if (titleNode.type === "TEXT") {
          // Single title

          Utils.processTitleNode(titleNode, data, 'eael_cta_title', 'eael-cta-box-title');
        } else {
          // Multiple titles - similar to dual color heading
          const textNodes = Utils.findTextNodes(titleNode as FrameNode);
          if (textNodes.length > 0) {
            data.settings.eael_cta_enable_multi_color_title = "yes";
            data.settings.eael_cta_multi_color_title = [];

            textNodes.forEach((textNode) => {
              const repeaterItem = Utils.processRepeaterTitleNode(textNode, data, `eael_cta_title`, `eael-cta-box-title`);
              data.settings.eael_cta_multi_color_title.push(repeaterItem);
            });
          }
        }
      }

      // Process content
      if (contentNode) {
        let textItems: string[] = [];

        if (contentNode.type === "TEXT") {
          if ("characters" in contentNode) {
            textItems.push(`<p>${contentNode.characters}</p>`);
          }
          WidgetUtils.processTypographySettings(contentNode, data, "eael-cta-box-content");
          WidgetUtils.processTextColor(contentNode, data, "eael-cta-box-content");
        } else {
          const textNodes = Utils.findTextNodes(contentNode as FrameNode);
          textNodes.forEach((textNode) => {
            if ("characters" in textNode) {
              textItems.push(`<p>${textNode.characters}</p>`);
            }
          });

          if (textNodes.length > 0) {
            WidgetUtils.processTypographySettings(textNodes[0], data, "eael-cta-box-content");
            WidgetUtils.processTextColor(textNodes[0], data, "eael-cta-box-content");
          }
        }

        data.settings.eael_cta_content = textItems.length > 0 ? textItems.join("") : "";
      }

      // Process primary button
      if (buttonNode) {
        Utils.processButtonNode(buttonNode, data, 'eael_cta_btn_text', 'eael-cta-box-button');

        const buttonIconNode = (buttonNode as any).children?.find((child: any) => child.name.startsWith('icon-'));

        if (buttonIconNode) {
          let args = {
            sizeKey: 'eael_cta_primary_btn_icon_size',
            // buttonGapKey: 'eael_cta_primary_btn_icon_indent',
            alignmentKey: 'eael_cta_btn_primary_icon_direction',
          };

          data.settings.eael_cta_primary_btn_icon_show = "yes";

          this.processButtonIcon(buttonNode, buttonIconNode, data, 'eael_cta_btn_primary_icon', 'eael_cta_primary_btn_icon_rotation', args);
        }
      }

      // Process secondary button
      if (secondaryButtonNode) {
        data.settings.eael_cat_secondary_btn_normal_border_border = "none";
        data.settings.eael_cta_secondary_btn_is_show = "yes";

        Utils.processButtonNode(secondaryButtonNode, data, 'eael_cta_secondary_btn_text', 'eael-cta-box-button-secondary');

        const buttonIconNode = (secondaryButtonNode as any).children?.find((child: any) => child.name.startsWith('icon-'));

        if (buttonIconNode) {
          let args = {
            sizeKey: 'eael_cta_secondary_btn_icon_size',
            // buttonGapKey: 'eael_cta_btn_secondary_icon_indent',
            alignmentKey: 'eael_cta_btn_secondary_icon_direction',
          };

          data.settings.eael_cta_secondary_btn_icon_show = "yes";

          this.processButtonIcon(secondaryButtonNode, buttonIconNode, data, 'eael_cta_btn_secondary_icon', 'eael_cta_secondary_btn_icon_rotation', args);
        }
      }

      // Process container background
      WidgetUtils.processBackground(node, data, 'eael-cta-box-container');
      WidgetUtils.processPadding(node, data, 'eael-cta-box-container');
      WidgetUtils.processBorderRadius(node, data, 'eael-cta-box-container');
    }
  }

  private static findAllButtons(node: FrameNode): any[] {
    const buttons: any[] = [];

    const findButtonsRecursively = (currentNode: any) => {
      if (!currentNode) return;

      // Check if current node is a button
      if (this.isButtonNode(currentNode)) {
        buttons.push(currentNode);
      }

      // Check children recursively
      if (currentNode.children && Array.isArray(currentNode.children)) {
        for (const child of currentNode.children) {
          findButtonsRecursively(child);
        }
      }
    };

    findButtonsRecursively(node);
    return buttons;
  }

  private static isButtonNode(node: any): boolean {
    if (!node || !node.name) return false;

    const nodeName = node.name.toLowerCase();

    // Check display name map
    const buttonDisplayNames = displayNameMap['button'] || [];
    const isButtonByDisplayName = buttonDisplayNames.some(name =>
      nodeName === name.toLowerCase() ||
      nodeName.includes(name.toLowerCase())
    );

    // // Check keywords
    // const matchesKeywords = nodeName.includes('button') ||
    //                        nodeName.includes('btn') ||
    //                        nodeName === 'button';

    // return isButtonByDisplayName || matchesKeywords;
    return isButtonByDisplayName;
  }

  private static processButtonIcon(buttonNode: any, buttonIconNode: any, data: any, iconKey: string, rotationKey?: string, args?: any): void {
    Utils.processIconNode(buttonIconNode, data, iconKey, rotationKey); // add icon rotation key later when available.

    if (args) {
      // icon size: if args['sizeKey'] is provided, use it as the icon size
      if (args['sizeKey']) {
        const iconSize = buttonIconNode.width;

        data.settings[args['sizeKey']] = {
          unit: "px",
          size: iconSize,
          // sizes: [],
        };
      }

      // icon indent: if args['indentKey'] is provided, use it as the icon indent
      if (args['buttonGapKey']) {
        const buttonGap = "itemSpacing" in buttonNode ? buttonNode.itemSpacing : "";
        if (buttonGap) {
          data.settings[args['buttonGapKey']] = {
            unit: "px",
            size: buttonGap,
          };
        }
      }

      // icon alignment: if args['alignmentKey'] is provided, use it as the icon alignment
      if (args['alignmentKey']) {
        const iconIndex = (buttonNode as any).children?.indexOf(buttonIconNode);
        const textIndex = (buttonNode as any).children?.indexOf(Utils.findTextNodes(buttonNode as FrameNode)[0]);

        if (iconIndex >= 0 && textIndex >= 0) {
          data.settings[args['alignmentKey']] =
            iconIndex > textIndex ? "right" : "left";
        }
      }
    }
  }
}