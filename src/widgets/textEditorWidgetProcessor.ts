import { WidgetUtils } from '../utils/widgetUtils';
import { Utils } from '../utils/utils';
import { ColorUtils } from '../utils/colorUtils';
import { ElementData, SceneNode, FrameNode } from '../types/figma';

export class TextEditorWidgetProcessor {
  public static process(node: SceneNode, data: ElementData): void {
    if (!node || !data || Array.isArray(data.settings)) {
      return;
    }

    // Process text content
    if (node.type === 'TEXT') {
      data.settings.editor = `<p>${node.characters}</p>`;

      // Process typography settings and text color
      WidgetUtils.processTypographySettings(node, data, "text-editor-text");
      WidgetUtils.processTextColor(node, data, "text-editor-text");
    }

    // Process height
    if (node.height) {
      data.settings.height = "min-height";
      data.settings.custom_height = {
        unit: "px",
        size: node.height,
        sizes: []
      };
    }
  }
} 