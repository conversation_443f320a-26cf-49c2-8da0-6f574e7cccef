export interface RGB {
  r: number;
  g: number;
  b: number;
}

export interface FontName {
  family: string;
  style: string;
}

export interface LineHeight {
  value: number;
  unit: 'PIXELS' | 'PERCENT';
}

export interface LetterSpacing {
  value: number;
  unit: 'PIXELS' | 'PERCENT';
}

export interface Paint {
  type: PaintType;
  visible?: boolean;
  opacity?: number;
  color?: RGB;
  blendMode?: BlendMode;
  gradientStops?: readonly ColorStop[];
  gradientTransform?: Transform;
}

export interface ColorStop {
  position: number;
  color: RGB;
}

export interface Transform {
  a: number;
  b: number;
  c: number;
  d: number;
  tx: number;
  ty: number;
}

export interface Vector {
  x: number;
  y: number;
}

export interface Effect {
  type: EffectType;
  visible?: boolean;
  radius?: number;
  color?: RGB;
  offset?: Vector;
  spread?: number;
  blendMode?: BlendMode;
}

export interface ExportSetting {
  suffix: string;
  format: ExportFormat;
  constraint: Constraint;
}

export interface Constraint {
  type: 'SCALE' | 'WIDTH' | 'HEIGHT';
  value: number;
}

export interface ComponentPropertyReferences {
  [key: string]: string;
}

export interface ComponentProperties {
  [key: string]: ComponentProperty;
}

export interface ComponentPropertyDefinitions {
  [key: string]: ComponentPropertyDefinition;
}

export interface ComponentProperty {
  type: ComponentPropertyType;
  value: boolean | string | number;
}

export interface ComponentPropertyDefinition {
  type: ComponentPropertyType;
  defaultValue: boolean | string | number;
}

export interface BaseNode {
  id: string;
  name: string;
  type: string;
  visible: boolean;
  locked: boolean;
  parent: BaseNode | null;
}

export interface ChildrenMixin {
  children: readonly SceneNode[];
}

export interface LayoutMixin {
  layoutMode?: 'NONE' | 'HORIZONTAL' | 'VERTICAL';
  primaryAxisSizingMode?: 'FIXED' | 'AUTO';
  counterAxisSizingMode?: 'FIXED' | 'AUTO';
  primaryAxisAlignItems?: 'MIN' | 'CENTER' | 'MAX' | 'SPACE_BETWEEN';
  counterAxisAlignItems?: 'MIN' | 'CENTER' | 'MAX' | 'SPACE_BETWEEN';
  paddingTop?: number;
  paddingRight?: number;
  paddingBottom?: number;
  paddingLeft?: number;
  marginTop?: number;
  marginRight?: number;
  marginBottom?: number;
  marginLeft?: number;
  itemSpacing?: number;
  layoutWrap?: 'NO_WRAP' | 'WRAP';
  layoutAlign?: 'STRETCH' | 'INHERIT';
  layoutGrow?: number;
  layoutPositioning?: 'AUTO' | 'ABSOLUTE';
}

export interface GeometryMixin {
  fills?: readonly Paint[];
  strokes?: readonly Paint[];
  strokeWeight?: number;
  cornerRadius?: number;
  width?: number;
  height?: number;
  absoluteTransform?: Transform;
  relativeTransform?: Transform;
  x?: number;
  y?: number;
  rotation?: number;
}

export interface TextNode extends BaseNode, GeometryMixin {
  type: 'TEXT';
  characters: string;
  fontSize?: number;
  fontName?: FontName;
  lineHeight?: LineHeight;
  letterSpacing?: LetterSpacing;
  textAlignHorizontal?: 'LEFT' | 'CENTER' | 'RIGHT' | 'JUSTIFIED';
  textAlignVertical?: 'TOP' | 'CENTER' | 'BOTTOM';
  textAutoResize?: 'NONE' | 'WIDTH_AND_HEIGHT' | 'HEIGHT';
  textDecoration?: 'NONE' | 'UNDERLINE' | 'STRIKETHROUGH';
  textCase?: 'ORIGINAL' | 'UPPER' | 'LOWER' | 'TITLE';
  paragraphIndent?: number;
  paragraphSpacing?: number;
  textStyleId?: string;
}

export interface FrameNode extends BaseNode, ChildrenMixin, LayoutMixin, GeometryMixin {
  type: 'FRAME';
  opacity?: number;
  blendMode?: BlendMode;
  isMask?: boolean;
  effects?: readonly Effect[];
  effectStyleId?: string;
  backgroundStyleId?: string;
  fillStyleId?: string;
  strokeStyleId?: string;
  gridStyleId?: string;
  exportSettings?: readonly ExportSetting[];
  componentPropertyReferences?: ComponentPropertyReferences;
  componentProperties?: ComponentProperties;
  componentPropertyDefinitions?: ComponentPropertyDefinitions;
  clipsContent?: boolean;
  guides?: readonly Vector[];
  layoutGrids?: readonly LayoutGrid[];
}

export interface LayoutGrid {
  pattern: 'GRID' | 'ROWS' | 'COLUMNS';
  sectionSize: number;
  visible: boolean;
  color: RGB;
  alignment: 'MIN' | 'CENTER' | 'MAX' | 'STRETCH';
  gutterSize: number;
  offset: number;
  count: number;
}

export interface GroupNode extends BaseNode, ChildrenMixin, GeometryMixin {
  type: 'GROUP';
  opacity?: number;
  blendMode?: BlendMode;
  isMask?: boolean;
  effects?: readonly Effect[];
  effectStyleId?: string;
  backgroundStyleId?: string;
  fillStyleId?: string;
  strokeStyleId?: string;
  gridStyleId?: string;
  exportSettings?: readonly ExportSetting[];
}

export interface SliceNode extends BaseNode, ChildrenMixin, GeometryMixin {
  type: 'SLICE';
  exportSettings?: readonly ExportSetting[];
}

export interface ComponentNode extends BaseNode, ChildrenMixin, GeometryMixin {
  type: 'COMPONENT';
  exportSettings?: readonly ExportSetting[];
}

export interface InstanceNode extends BaseNode, ChildrenMixin, GeometryMixin {
  type: 'INSTANCE';
  exportSettings?: readonly ExportSetting[];
}

export interface BooleanOperationNode extends BaseNode, ChildrenMixin, GeometryMixin {
  type: 'BOOLEAN_OPERATION';
  exportSettings?: readonly ExportSetting[];
}

export interface VectorNode extends BaseNode, GeometryMixin {
  type: 'VECTOR';
  exportSettings?: readonly ExportSetting[];
}

export interface StarNode extends BaseNode, GeometryMixin {
  type: 'STAR';
  exportSettings?: readonly ExportSetting[];
}

export interface LineNode extends BaseNode, GeometryMixin {
  type: 'LINE';
  exportSettings?: readonly ExportSetting[];
}

export interface EllipseNode extends BaseNode, GeometryMixin {
  type: 'ELLIPSE';
  exportSettings?: readonly ExportSetting[];
}

export interface PolygonNode extends BaseNode, GeometryMixin {
  type: 'POLYGON';
  exportSettings?: readonly ExportSetting[];
}

export interface RectangleNode extends BaseNode, GeometryMixin {
  type: 'RECTANGLE';
  exportSettings?: readonly ExportSetting[];
}

export interface StickyNode extends BaseNode, GeometryMixin {
  type: 'STICKY';
  exportSettings?: readonly ExportSetting[];
}

export interface ConnectorNode extends BaseNode, GeometryMixin {
  type: 'CONNECTOR';
  exportSettings?: readonly ExportSetting[];
}

export interface ShapeWithTextNode extends BaseNode, GeometryMixin {
  type: 'SHAPE_WITH_TEXT';
  exportSettings?: readonly ExportSetting[];
}

export interface CodeBlockNode extends BaseNode, GeometryMixin {
  type: 'CODE_BLOCK';
  exportSettings?: readonly ExportSetting[];
}

export interface StampNode extends BaseNode, GeometryMixin {
  type: 'STAMP';
  exportSettings?: readonly ExportSetting[];
}

export interface WidgetNode extends BaseNode, GeometryMixin {
  type: 'WIDGET';
  exportSettings?: readonly ExportSetting[];
}

export interface EmbedNode extends BaseNode, GeometryMixin {
  type: 'EMBED';
  exportSettings?: readonly ExportSetting[];
}

export interface LinkUnfurlNode extends BaseNode, GeometryMixin {
  type: 'LINK_UNFURL';
  exportSettings?: readonly ExportSetting[];
}

export interface MediaNode extends BaseNode, GeometryMixin {
  type: 'MEDIA';
  exportSettings?: readonly ExportSetting[];
}

export interface SectionNode extends BaseNode, GeometryMixin {
  type: 'SECTION';
  exportSettings?: readonly ExportSetting[];
}

export interface HighlightNode extends BaseNode, GeometryMixin {
  type: 'HIGHLIGHT';
  exportSettings?: readonly ExportSetting[];
}

export interface WashiTapeNode extends BaseNode, GeometryMixin {
  type: 'WASHI_TAPE';
  exportSettings?: readonly ExportSetting[];
}

export interface ShapeNode extends BaseNode, GeometryMixin {
  type: 'SHAPE';
  exportSettings?: readonly ExportSetting[];
}

export type SceneNode = TextNode | FrameNode | GroupNode | SliceNode | ComponentNode | InstanceNode | BooleanOperationNode | VectorNode | StarNode | LineNode | EllipseNode | PolygonNode | RectangleNode | StickyNode | ConnectorNode | ShapeWithTextNode | CodeBlockNode | StampNode | WidgetNode | EmbedNode | LinkUnfurlNode | MediaNode | SectionNode | HighlightNode | WashiTapeNode | ShapeNode;

export type PaintType = 'SOLID' | 'GRADIENT_LINEAR' | 'GRADIENT_RADIAL' | 'GRADIENT_ANGULAR' | 'GRADIENT_DIAMOND' | 'IMAGE' | 'EMOJI';
export type BlendMode = 'PASS_THROUGH' | 'NORMAL' | 'DARKEN' | 'MULTIPLY' | 'LINEAR_BURN' | 'COLOR_BURN' | 'LIGHTEN' | 'SCREEN' | 'LINEAR_DODGE' | 'COLOR_DODGE' | 'OVERLAY' | 'SOFT_LIGHT' | 'HARD_LIGHT' | 'DIFFERENCE' | 'EXCLUSION' | 'HUE' | 'SATURATION' | 'COLOR' | 'LUMINOSITY';
export type EffectType = 'INNER_SHADOW' | 'DROP_SHADOW' | 'LAYER_BLUR' | 'BACKGROUND_BLUR';
export type ExportFormat = 'JPG' | 'PNG' | 'SVG' | 'PDF';
export type ComponentPropertyType = 'BOOLEAN' | 'TEXT' | 'INSTANCE_SWAP' | 'VARIANT';

export interface ElementData {
  id: string;
  settings: Record<string, any>;
  elements: ElementData[];
  isInner: boolean;
  elType: string;
  widgetType?: string;
}

export interface JsonResult {
  content: ElementData[];
  page_settings?: any[];
  version: string;
  title: string;
  type: string;
} 