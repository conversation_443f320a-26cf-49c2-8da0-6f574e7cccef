import { ElementTypeResolver } from '../utils/elementTypeResolver';
import * as WidgetProcessors from '../widgets';
import { JsonResult, SceneNode, FrameNode, ElementData } from '../types/figma';
import { WidgetUtils } from '../utils/widgetUtils';
import { PLUGIN_TITLE, PLUGIN_VERSION } from '../code';
import { widgetProcessors } from '../config/widgetSettings';

export class SelectionProcessor {

  public static nodeIds: string[] = []; // Store all matching node IDs

  /**
 * Process the selection and return a JSON result
 */
  public static async processSelection(selection: readonly SceneNode[]): Promise<JsonResult> {
    // Reset nodeIds array before collecting new ones
    this.nodeIds = [];

    // Collect image node IDs from the selection
    this.collectNodeIds(selection as any);

    // Process the selection to generate content
    let content = selection.map((node) => this.processNode(node))
      .filter(elementData => elementData !== null);

    // Ensure there's at least one container in the structure
    content = this.ensureContainerStructure(content);

    const fileName = PLUGIN_TITLE + `-for-elementor-${new Date().getTime()}.json`;

    return {
      content,
      // page_settings: [],
      // page_settings: [{
      //   layout: 'elementor_canvas',
      // }],
      version: PLUGIN_VERSION,
      // title: PLUGIN_TITLE,
      title: fileName,
      type: "container"
    };
  }

  /**
   * Ensures that the content has exactly one container node at the top level
   * If there are no containers, wraps all elements in a single container
   * If there are multiple containers, keeps them as is
   */
  private static ensureContainerStructure(content: ElementData[]): ElementData[] {
    // Check if there's at least one container at the top level (also check for legacy 'section' for backward compatibility)
    const hasContainer = content.some(item =>
      item.elType === 'container' || item.elType === 'section'
    );

    // If there's already a container, return the content as is
    if (hasContainer) {

      return content;
    }

    // If all elements are widgets or other non-container elements, wrap them in a single container


    // Create a container to wrap all elements
    const containerWrapper: ElementData = {
      id: this.generate8CharHash(),
      settings: {},
      elements: content,
      isInner: false,
      elType: 'container'
    };

    // Return the single container as the new top-level element
    return [containerWrapper];
  }

  private static processNode(node: SceneNode): ElementData | null {
    if (!node) {
      return {
        id: this.generate8CharHash(),
        settings: [],
        elements: [],
        isInner: false,
        elType: 'container'
      };
    }

    // Skip hidden nodes - they should not be processed
    if (node.visible === false) {

      return null;
    }

    const elType = ElementTypeResolver.getElementType(node);
    const isInner = node.name?.includes("inner") || false;
    const baseData: ElementData = {
      id: this.generate8CharHash(),
      settings: {},
      elements: [],
      isInner,
      elType
    };

    // Process container settings if it's a container
    if (elType === 'container') {
      // Process common container settings
      WidgetUtils.processBackground(node, baseData);
      WidgetUtils.processBorderRadius(node, baseData);
      WidgetUtils.processBorderWidth(node, baseData, 'container');
      WidgetUtils.processPadding(node, baseData);
      WidgetUtils.processMargin(node, baseData);

      // Process widget-specific settings
      WidgetUtils.processWidgetSettings(node, baseData);
    }

    // Auto-detect widget type - this will work even without explicit naming
    // Note: Auto-detection can be enabled/disabled via appConfig.features.enableAutoWidgetDetection
    const widgetType = ElementTypeResolver.getWidgetType(node);

    if (widgetType !== undefined) {
      baseData.widgetType = widgetType;
      // When a widget type is found, always set elType to "widget"
      baseData.elType = 'widget';

      // Process widget-specific settings
      this.processWidgetByType(node, baseData);
    }

    // Process children for non-widget elements
    if ('children' in node && Array.isArray(node.children) &&
        !baseData.widgetType?.startsWith('eael')
        && baseData.widgetType !== "button"
        && baseData.widgetType !== "heading"
        && baseData.widgetType !== "text-editor") {
      baseData.elements = node.children
        .map(child => this.processNode(child))
        .filter(elementData => elementData !== null);
    }

    // Clean up empty settings
    if (Object.keys(baseData.settings).length === 0) {
      baseData.settings = [];
    }

    return baseData;
  }

  private static processWidgetByType(node: SceneNode, baseData: ElementData) {
    const processor = widgetProcessors[baseData.widgetType];
    if (processor) {
      processor.process(node, baseData);
    } else if (baseData.widgetType.startsWith('eael-advanced-menu')) {
      if (node.type === "FRAME") {
        WidgetProcessors.AdvancedMenuWidgetProcessor.process(node as FrameNode, baseData);
      }
    }
  }

  private static generate8CharHash(): string {
    return Math.random().toString(36).substring(2, 10); // 8 chars
  }

  // This section was intentionally removed as it's no longer needed.
  // Image processing is now handled in the PluginUI class.

  // Helper function to collect valid node IDs
  public static collectNodeIds(nodes: readonly any[]) {
    for (const node of nodes) {
      // Skip hidden nodes when collecting image IDs
      if (node.visible === false) {
        continue;
      }

      // Check for image nodes by name or type
      if ((node.name.toLowerCase().startsWith("image") || node.type === "RECTANGLE") && "fills" in node) {
        // Check if the node has an image fill
        const hasFills = node.fills && Array.isArray(node.fills) && node.fills.length > 0;
        const hasImageFill = hasFills && node.fills.some((fill: any) => fill.type === "IMAGE");

        if (hasImageFill) {
          this.nodeIds.push(node.id);
        }
      }

      // Process children recursively (only for visible nodes)
      if ("children" in node && Array.isArray(node.children)) {
        this.collectNodeIds(node.children);
      }
    }
  }
  //
}