/**
 * Application Configuration
 *
 * This file contains global configuration options and feature flags
 * that control the behavior of the Figma to WordPress plugin.
 */

export interface AppConfig {
  /**
   * Feature Flags
   */
  features: {
    /**
     * Enable automatic widget type detection when frame names don't match
     *
     * When enabled, the system will try to infer widget types based on
     * the structure and content of frames even when they don't have
     * explicit widget naming.
     *
     * Performance Impact: HIGH - This feature performs extensive analysis
     * of frame structure, children, and content which can slow down processing
     *
     * @default false
     */
    enableAutoWidgetDetection: boolean;

    /**
     * Enable automatic node detection when frame names don't match display names
     *
     * When enabled, findNodeRecursively will try to detect nodes based on
     * content patterns and structure analysis when display name matching fails.
     *
     * Performance Impact: MEDIUM - This feature performs content analysis
     * and pattern matching which can slow down node finding operations
     *
     * @default false
     */
    enableAutoNodeDetection: boolean;
  };

  /**
   * Performance Settings
   */
  performance: {
    /**
     * Maximum frame size for auto-detection analysis
     * Frames larger than this will be skipped during auto-detection
     */
    maxAutoDetectionFrameWidth: number;
    maxAutoDetectionFrameHeight: number;
  };
}

/**
 * Default application configuration
 *
 * Note: Auto-detection is disabled by default for performance reasons.
 * Enable it only when needed for specific use cases.
 */
export const defaultAppConfig: AppConfig = {
  features: {
    enableAutoWidgetDetection: false, // Disabled by default for performance
    enableAutoNodeDetection: false, // Disabled by default for performance
  },
  performance: {
    maxAutoDetectionFrameWidth: 800,
    maxAutoDetectionFrameHeight: 600,
  },
};

/**
 * Current application configuration
 *
 * QUICK START - To enable auto-detection:
 * 1. Uncomment the features block below
 * 2. Set enableAutoWidgetDetection and/or enableAutoNodeDetection to true
 * 3. Rebuild the plugin with: npm run build
 *
 * WARNING: Auto-detection has performance impact.
 * Only enable when you need to process designs without explicit naming.
 */
export const appConfig: AppConfig = {
  ...defaultAppConfig,
  // Override specific settings here if needed
  // features: {
  //   ...defaultAppConfig.features,
  //   enableAutoWidgetDetection: true, // Uncomment to enable widget auto-detection
  //   enableAutoNodeDetection: true, // Uncomment to enable node auto-detection
  // },
};

/**
 * EXAMPLE: How to enable auto-detection for a release
 *
 * Simply uncomment and modify the configuration above, or replace it with:
 *
 * export const appConfig: AppConfig = {
 *   ...defaultAppConfig,
 *   features: {
 *     ...defaultAppConfig.features,
 *     enableAutoWidgetDetection: true,
 *     enableAutoNodeDetection: true,
 *   },
 * };
 */

/**
 * Helper function to check if a feature is enabled
 */
export const isFeatureEnabled = (featureName: keyof AppConfig['features']): boolean => {
  return appConfig.features[featureName];
};

/**
 * Helper function to get performance settings
 */
export const getPerformanceSetting = (settingName: keyof AppConfig['performance']): number => {
  return appConfig.performance[settingName];
};

/**
 * Development helper function to temporarily enable/disable auto-detection
 * This can be useful for testing or debugging purposes
 *
 * @param enabled - Whether to enable auto-detection
 */
export const setAutoDetectionEnabled = (enabled: boolean): void => {
  appConfig.features.enableAutoWidgetDetection = enabled;
};

/**
 * Get current status of auto-detection feature
 */
export const isAutoDetectionEnabled = (): boolean => {
  return appConfig.features.enableAutoWidgetDetection;
};
