import { Utils } from './utils';
import { SceneNode, FrameNode } from '../types/figma';
import { widgetDisplayNameMap, displayNameMap } from '../config/widgetSettings';
import { isFeatureEnabled, getPerformanceSetting } from '../config/appConfig';

export class ElementTypeResolver {
  private static readonly WIDGET_PREFIXES = [
    'widget-eael-',
    'widget-ea-',
    'widget-',
    'eael-',
    'ea-'
  ] as const;

  static getElementType(node: SceneNode): string {
    // First check if this is a widget - if so, always return 'widget'
    if (this.isWidget(node)) {
      return 'widget';
    }

    const typeMap: Record<string, string> = {
      'FRAME': 'container',
      'GROUP': 'column',
      'TEXT': 'widget'
    };

    let type = typeMap[node.type] || 'widget';

    if (node.type === 'FRAME') {
      const frameNode = node as FrameNode;
      if (frameNode.name.toLowerCase().startsWith('container')) {
        type = 'container';
        return type;
      }
    }

    const specialNames = ['section', 'inner-section', 'column', 'inner-column',
      'inner-column-6', 'inner-column-4', 'inner-column-3'];

    if (specialNames.includes(node.name)) {
      type = node.name.includes('column') ? 'column' : 'container';
    }

    // Find container parent by checking if any parent node has 'container' in its name
    let parent = node;
    while (parent && 'parent' in parent) {
      if (parent.name.toLowerCase().includes('container')) {
        type = type === 'container' ? 'container' : type;
        break;
      }
      parent = parent.parent as SceneNode;
    }

    return type;
  }

  // Get Widget Name / Type / Slug; e.x. widget-eael-info-box => eael-info-box; Info Box => eael-info-box
  static getWidgetType(node: SceneNode): string | undefined {
    // First try to get widget type from name
    const namedWidgetType = this.getWidgetTypeFromName(node);
    if (namedWidgetType) {
      return namedWidgetType;
    }

    // Check if auto-detection is enabled before performing expensive structure analysis
    if (!isFeatureEnabled('enableAutoWidgetDetection')) {
      return undefined;
    }

    // If no widget type found from name and auto-detection is enabled, try to infer from structure
    return this.inferWidgetTypeFromStructure(node);
  }

  private static getWidgetTypeFromName(node: SceneNode): string | undefined {
    if (node.type === 'FRAME' || node.type === 'TEXT') {
      const frameNode = node as FrameNode;
      let widgetName = frameNode.name;

      // Remove any matching prefix from the widget name
      for (const prefix of this.WIDGET_PREFIXES) {
        if (widgetName.toLowerCase().startsWith(prefix.toLowerCase())) {
          widgetName = widgetName.substring(prefix.length);
          break;
        }
      }

      // Convert to slug for lookup
      let slugName = this.getSlugFromName(widgetName);

      // for advanced menu it may contain menu id
      if (slugName.includes('advanced-menu') && slugName.length > 13) {
        // get the digit from the name and add with advanced-menu
        const menuIdMatch = slugName.match(/(\d+)$/);
        if (menuIdMatch) {
          slugName = `eael-advanced-menu-${menuIdMatch[1]}`;
        }
        return slugName;
      }

      return widgetDisplayNameMap[slugName] ?? (node.type === 'TEXT' ? 'heading' : undefined);
    }

    return undefined;
  }

  private static inferWidgetTypeFromStructure(node: SceneNode): string | undefined {
    if (node.type !== 'FRAME') return undefined;

    const frameNode = node as FrameNode;
    if (!frameNode.children || frameNode.children.length === 0) return undefined;

    // Add size constraints - very large frames are likely containers, not widgets
    // Use configurable thresholds for better performance control
    const maxWidth = getPerformanceSetting('maxAutoDetectionFrameWidth');
    const maxHeight = getPerformanceSetting('maxAutoDetectionFrameHeight');
    const isVeryLarge = frameNode.width > maxWidth || frameNode.height > maxHeight;
    if (isVeryLarge) {
      return undefined;
    }

    // Check if this frame contains multiple widget-like children
    // If so, it's likely a container, not a widget itself
    const widgetLikeChildren = this.countWidgetLikeChildren(frameNode);
    if (widgetLikeChildren >= 2) {
      return undefined;
    }

    // IMPORTANT: Check if this frame contains children with explicit widget names
    // If so, this frame is likely a container for those widgets, not a widget itself
    const hasExplicitWidgetChildren = this.hasExplicitWidgetChildren(frameNode);
    if (hasExplicitWidgetChildren) {
      return undefined;
    }

    // Check for common widget patterns
    const hasTitle = this.hasChildOfType(frameNode, 'title');
    const hasContent = this.hasChildOfType(frameNode, 'content');
    const hasButton = this.hasChildOfType(frameNode, 'button');
    const hasImage = this.hasChildOfType(frameNode, 'image');
    const hasIcon = this.hasChildOfType(frameNode, 'icon');
    const hasMenuItem = this.hasChildOfType(frameNode, 'menu-item');



    // If explicit child elements aren't found, try to detect them by structure
    // But be more conservative - only look at direct children, not deep descendants
    const hasImplicitTitle = this.hasImplicitTitleDirect(frameNode);
    const hasImplicitContent = this.hasImplicitContentDirect(frameNode);
    const hasImplicitButton = this.hasImplicitButtonDirect(frameNode);

    // Check for multiple buttons (common in CTA boxes)
    const hasMultipleButtons = this.hasMultipleButtonsDirect(frameNode);

    // Check for styled heading (like "Creative" in different style)
    const hasStyledHeading = this.hasStyledHeading(frameNode);

    // Infer widget type based on child elements (explicit or implicit)
    if (hasMenuItem) {
      return 'eael-advanced-menu';
    }
    // CTA Box detection - prioritize this over simple button
    else if (((hasTitle || hasImplicitTitle || hasStyledHeading) &&
              (hasButton || hasImplicitButton || hasMultipleButtons))) {
      return 'eael-cta-box';
    }
    // Info Box detection
    else if ((hasTitle || hasImplicitTitle || hasStyledHeading) &&
             (hasContent || hasImplicitContent) &&
             (hasButton || hasImplicitButton) &&
             (hasImage || hasIcon)) {

      // CRITICAL CHECK: Make sure this isn't just a node with only subheading children
      const hasOnlySubheadings = frameNode.children?.every(child =>
        child.type === 'TEXT' && this.isSubtitleTerm(child.name.toLowerCase())
      );

      if (hasOnlySubheadings) {
        return undefined;
      }

      return 'eael-info-box';
    }
    // Testimonial detection
    else if ((hasTitle || hasImplicitTitle) &&
             (hasContent || hasImplicitContent) &&
             (hasImage)) {
      return 'eael-testimonial';
    }
    // Simple button detection - lowest priority
    else if (hasButton || hasImplicitButton) {
      return 'button';
    }
    // Heading detection
    else if ((hasTitle || hasImplicitTitle || hasStyledHeading) &&
             !(hasContent || hasImplicitContent)) {

      // CRITICAL CHECK: Make sure this isn't just a node with only subheading children
      const hasOnlySubheadings = frameNode.children?.every(child =>
        child.type === 'TEXT' && this.isSubtitleTerm(child.name.toLowerCase())
      );

      if (hasOnlySubheadings) {
        return undefined;
      }

      return 'heading';
    }
    // Text editor detection
    else if ((hasContent || hasImplicitContent) &&
             !(hasTitle || hasImplicitTitle)) {
      return 'text-editor';
    }

    return undefined;
  }

  private static hasExplicitWidgetChildren(node: FrameNode): boolean {
    if (!node.children) {
      return false;
    }

    // Check direct children first
    for (const child of node.children) {
      // if (child.type === 'FRAME') {
        const childFrame = child as FrameNode;
        // Check if this child has explicit widget naming
        const widgetType = this.getWidgetTypeFromName(childFrame);
        if (widgetType) {
          return true;
        }
      // }
    }

    // If no direct explicit widgets found, check recursively in child containers
    for (const child of node.children) {
      if (child.type === 'FRAME') {
        const childFrame = child as FrameNode;
        // Recursively check if this child container has explicit widgets
        if (this.hasExplicitWidgetChildren(childFrame)) {
          return true;
        }
      }
    }

    return false;
  }

  private static countWidgetLikeChildren(node: FrameNode): number {
    if (!node.children) return 0;

    let count = 0;
    for (const child of node.children) {
      if (child.type === 'FRAME') {
        const childFrame = child as FrameNode;
        // Check if this child looks like a widget
        if (this.looksLikeWidget(childFrame)) {
          count++;
        }
      }
    }
    return count;
  }

  private static looksLikeWidget(node: FrameNode): boolean {
    // First check if it has explicit widget naming - if so, it's definitely a widget
    if (this.getWidgetTypeFromName(node)) {
      return true;
    }

    // A frame looks like a widget if it has a reasonable size and contains widget-like elements
    const hasReasonableSize = node.width < 600 && node.height < 400;
    if (!hasReasonableSize) return false;

    // Check if it has widget-like content
    const hasTitle = this.hasChildOfType(node, 'title') || this.hasImplicitTitleDirect(node);
    const hasContent = this.hasChildOfType(node, 'content') || this.hasImplicitContentDirect(node);
    const hasButton = this.hasChildOfType(node, 'button') || this.hasImplicitButtonDirect(node);
    const hasImage = this.hasChildOfType(node, 'image');

    // If it has at least 2 widget-like elements, it's probably a widget
    const elementCount = [hasTitle, hasContent, hasButton, hasImage].filter(Boolean).length;
    return elementCount >= 2;
  }

  private static hasImplicitTitleDirect(node: FrameNode): boolean {
    // Look for large text in direct children only
    if (!node.children) return false;

    for (const child of node.children) {
      if (child.type === 'TEXT') {
        const textNode = child as any;

        // CRITICAL: Don't treat text in subtitle-named nodes as title
        if (this.isSubtitleTerm(child.name.toLowerCase())) {
          continue;
        }

        if (textNode.fontSize !== undefined &&
            textNode.fontSize >= 16 &&
            textNode.characters.length < 50 &&
            !textNode.characters.includes('.')) {
          return true;
        }
      }
    }
    return false;
  }

  private static hasImplicitContentDirect(node: FrameNode): boolean {
    // Look for paragraph-like text in direct children only
    if (!node.children) return false;

    for (const child of node.children) {
      if (child.type === 'TEXT') {
        const textNode = child as any;

        // CRITICAL: Don't treat text in subtitle-named nodes as content
        if (this.isSubtitleTerm(child.name.toLowerCase())) {
          continue;
        }

        if (textNode.characters.length > 50 ||
            textNode.characters.includes('.') ||
            textNode.characters.includes('\n')) {
          return true;
        }
      }
    }
    return false;
  }

  private static hasImplicitButtonDirect(node: FrameNode): boolean {
    // Look for button-like elements in direct children only
    if (!node.children) return false;

    for (const child of node.children) {
      if (this.isButtonLike(child)) {
        return true;
      }
    }
    return false;
  }

  private static hasMultipleButtonsDirect(node: FrameNode): boolean {
    // Look for multiple button-like elements in direct children only
    if (!node.children) return false;

    let buttonCount = 0;
    for (const child of node.children) {
      if (this.isButtonLike(child)) {
        buttonCount++;
        if (buttonCount >= 2) return true;
      }
    }
    return false;
  }



  private static hasStyledHeading(node: FrameNode): boolean {
    // Look for headings with mixed styles (like "Creative" in a different color)
    if (!node.children) return false;

    // First look for large text nodes that might be headings
    const textNodes = Utils.findTextNodes(node);
    if (textNodes.length === 0) return false;

    // Check for large text nodes
    const largeTextNodes = textNodes.filter(text =>
      text.fontSize !== undefined && text.fontSize >= 24
    );

    if (largeTextNodes.length === 0) return false;

    // Check if any of these large text nodes have mixed styles
    for (const textNode of largeTextNodes) {
      // Check if the text has different styles within it
      if ('styleOverrideTable' in textNode &&
          textNode.styleOverrideTable &&
          Object.keys(textNode.styleOverrideTable).length > 0) {
        return true;
      }

      // Check if there are multiple text nodes close to each other with different styles
      const parent = Utils.findParentNode(node, textNode);
      if (parent && parent.type === 'FRAME') {
        const siblingTexts = parent.children?.filter(child =>
          child.type === 'TEXT' && child !== textNode
        );

        if (siblingTexts && siblingTexts.length > 0) {
          // Check if any sibling text has a different fill color
          const textFill = textNode.fills && textNode.fills.length > 0 ?
                          textNode.fills[0] : null;

          for (const sibling of siblingTexts) {
            const siblingFill = sibling.fills && sibling.fills.length > 0 ?
                              sibling.fills[0] : null;

            if (textFill && siblingFill &&
                JSON.stringify(textFill) !== JSON.stringify(siblingFill)) {
              return true;
            }
          }
        }
      }
    }

    return false;
  }

  static isButtonLike(node: SceneNode): boolean {
    // Check if node is explicitly named as a button
    if (node.name.toLowerCase().includes('button')) {
      return true;
    }

    // Check for text nodes that look like buttons
    if (node.type === 'TEXT') {
      const textNode = node as unknown as TextNode;
      const textContent = textNode.characters.toLowerCase();

      // Check for short, action-oriented text
      const isShortText = textContent.length < 30;
      const hasActionWord = textContent.match(Utils.getButtonActionPattern());

      return isShortText && (hasActionWord !== null);
    }

    // Check for frame nodes that look like buttons
    if (node.type === 'FRAME') {
      const frameNode = node as FrameNode;

      // Check size constraints
      const hasButtonSize = frameNode.width < 200 && frameNode.height < 80;

      // Check visual characteristics
      const hasBackground = frameNode.fills && frameNode.fills.length > 0;
      const hasBorderRadius = frameNode.cornerRadius !== undefined && frameNode.cornerRadius > 0;
      const hasBorder = frameNode.strokes && frameNode.strokes.length > 0;

      // Check for icon
      const hasIcon = frameNode.children?.some(child =>
        child.type === 'VECTOR' ||
        child.type === 'BOOLEAN_OPERATION' ||
        (child.type === 'FRAME' && child.width < 24 && child.height < 24)
      );

      // Check for button-like text content
      const textNodes = Utils.findTextNodes(frameNode);
      const hasButtonText = textNodes.some(textNode => {
        const textContent = textNode.characters.toLowerCase();
        const isShortText = textContent.length < 30;
        const hasActionWord = textContent.match(Utils.getButtonActionPattern());
        return isShortText && (hasActionWord !== null);
      });

      // A frame is button-like if it has at least two of:
      // - Button-like text
      // - Icon
      // - Button-like size
      // - Background color
      // - Border radius
      // - Border
      const characteristics = [
        hasButtonText,
        hasIcon,
        hasButtonSize,
        hasBackground,
        hasBorderRadius,
        hasBorder
      ];

      // Require at least two characteristics, OR button text AND an icon specifically
      const characteristicCount = characteristics.filter(Boolean).length;

      return characteristicCount >= 2 || (hasButtonText && hasIcon);
    }

    return false;
  }

  private static hasChildOfType(node: FrameNode, childType: string): boolean {
    // First check direct children by name
    for (const child of node.children) {
      if (child.name.toLowerCase() === childType.toLowerCase() ||
          child.name.toLowerCase().startsWith(childType.toLowerCase() + '-') ||
          child.name.toLowerCase().includes('-' + childType.toLowerCase())) {

        // Block subtitle terms from matching title searches
        if (childType === 'title' && this.isSubtitleTerm(child.name.toLowerCase())) {
          continue;
        }

        return true;
      }

      // Check against display name map
      if (displayNameMap[childType]) {
        for (const name of displayNameMap[childType]) {
          if (child.name.toLowerCase() === name.toLowerCase() ||
              child.name.toLowerCase().startsWith(name.toLowerCase() + '-') ||
              child.name.toLowerCase().includes('-' + name.toLowerCase())) {

            // Special case: When looking for 'title', exclude subtitle-related terms
            if (childType === 'title' && this.isSubtitleTerm(child.name.toLowerCase())) {
              continue;
            }

            return true;
          }
        }
      }
    }

    // Then check recursively
    for (const child of node.children) {
      if (child.type === 'FRAME') {
        const foundRecursively = Utils.findNodeRecursively(child, undefined, childType);
        if (foundRecursively) {
          // CRITICAL: Also check if the found node is a subtitle term when looking for title
          if (childType === 'title' && this.isSubtitleTerm(foundRecursively.name.toLowerCase())) {
            continue;
          }
          return true;
        }
      }
    }

    return false;
  }

  /**
   * Check if a term is subtitle-related and should not be considered as a main title
   */
  private static isSubtitleTerm(term: string): boolean {
    const normalizedTerm = term.toLowerCase().trim();

    // Exact matches for subtitle terms
    const subtitleTerms = [
      'subtitle', 'sub title', 'sub-title',
      'subheading', 'sub heading', 'sub-heading',
      'subtext', 'sub text', 'sub-text'
    ];

    // Check for exact matches or terms that START with subtitle prefixes
    return subtitleTerms.some(subtitleTerm =>
      normalizedTerm === subtitleTerm ||
      normalizedTerm.startsWith(subtitleTerm + '-') ||
      normalizedTerm.startsWith(subtitleTerm + '_') ||
      normalizedTerm.startsWith(subtitleTerm + ' ')
    );
  }

  static getSlugFromName(name: string): string {
    return name.toLowerCase().replace(/[-_ ]/g, '-').replace(/\s+/g, '-');
  }

  static isWidget(node: SceneNode): boolean {
    return this.getWidgetType(node) !== undefined;
  }
}