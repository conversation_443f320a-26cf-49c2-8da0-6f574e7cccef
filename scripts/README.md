# Build Scripts

This directory contains build and deployment scripts for the Figma to WordPress plugin.

## create-zip.js

Creates a distribution zip file containing only the necessary files for the Figma plugin:

- `manifest.json` - Plugin manifest
- `ui.html` - Plugin UI
- `dist/` - Compiled JavaScript files

### Usage

The script is automatically run as part of the build process, but can also be run independently:

```bash
# Build and create zip
npm run build

# Create zip only (requires dist/ folder to exist)
npm run zip

# Build without creating zip
npm run build:only
```

### Output

The script creates a timestamped zip file in the project root:
- Format: `figma-to-wp-plugin-YYYY-MM-DDTHH-MM-SS.zip`
- Example: `figma-to-wp-plugin-2025-05-27T10-16-08.zip`

### Features

- ✅ Validates required files exist before creating zip
- ✅ Includes compression for smaller file size
- ✅ Timestamped filenames to avoid conflicts
- ✅ Detailed console output with file sizes
- ✅ Error handling with clear messages
