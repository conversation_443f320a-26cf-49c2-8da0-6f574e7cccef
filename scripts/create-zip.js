const fs = require('fs');
const path = require('path');
const archiver = require('archiver');

/**
 * Creates a zip file containing the Figma plugin files
 * Includes: manifest.json, ui.html, and dist/ folder
 */
async function createPluginZip() {
  const projectRoot = path.resolve(__dirname, '..');
  const zipFileName = `figma-to-wp-plugin.zip`;
  const zipPath = path.join(projectRoot, zipFileName);

  // Create a file to stream archive data to
  const output = fs.createWriteStream(zipPath);
  const archive = archiver('zip', {
    zlib: { level: 9 } // Sets the compression level
  });

  // Listen for all archive data to be written
  output.on('close', function () {
    console.log(`✅ Plugin zip created successfully!`);
    console.log(`📦 File: ${zipFileName}`);
    console.log(`📊 Size: ${archive.pointer()} total bytes`);
    console.log(`📁 Location: ${zipPath}`);
  });

  // Listen for warnings (e.g., stat failures and other non-blocking errors)
  archive.on('warning', function (err) {
    if (err.code === 'ENOENT') {
      console.warn('⚠️  Warning:', err.message);
    } else {
      throw err;
    }
  });

  // Listen for errors
  archive.on('error', function (err) {
    console.error('❌ Error creating zip:', err);
    throw err;
  });

  // Pipe archive data to the file
  archive.pipe(output);

  try {
    // Check if required files exist
    const manifestPath = path.join(projectRoot, 'manifest.json');
    const uiPath = path.join(projectRoot, 'ui.html');
    const distPath = path.join(projectRoot, 'dist');

    if (!fs.existsSync(manifestPath)) {
      throw new Error('manifest.json not found in project root');
    }
    if (!fs.existsSync(uiPath)) {
      throw new Error('ui.html not found in project root');
    }
    if (!fs.existsSync(distPath)) {
      throw new Error('dist/ folder not found. Please run "npm run build" first.');
    }

    console.log('📋 Adding files to zip...');

    // Add manifest.json
    archive.file(manifestPath, { name: 'manifest.json' });
    console.log('   ✓ manifest.json');

    // Add ui.html
    archive.file(uiPath, { name: 'ui.html' });
    console.log('   ✓ ui.html');

    // Add entire dist directory
    archive.directory(distPath, 'dist');
    console.log('   ✓ dist/ folder');

    // Finalize the archive (i.e., we are done appending files but streams have to finish yet)
    await archive.finalize();

  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

// Run the script if called directly
if (require.main === module) {
  createPluginZip().catch(error => {
    console.error('❌ Failed to create zip:', error);
    process.exit(1);
  });
}

module.exports = createPluginZip;
