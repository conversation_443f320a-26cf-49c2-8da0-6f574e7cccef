{"name": "figma-to-wp", "version": "1.0.0", "description": "Figma to WP", "main": "code.js", "scripts": {"build": "webpack --config webpack.config.js && node scripts/create-zip.js", "build:watch": "webpack --config webpack.config.js --watch", "build:only": "webpack --config webpack.config.js", "zip": "node scripts/create-zip.js", "lint": "eslint --ext .ts,.tsx --ignore-pattern node_modules .", "lint:fix": "eslint --ext .ts,.tsx --ignore-pattern node_modules --fix .", "dev": "npm run build:watch"}, "author": "", "license": "", "devDependencies": {"@figma/eslint-plugin-figma-plugins": "*", "@figma/plugin-typings": "^1.104.0", "@types/archiver": "^6.0.3", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "archiver": "^7.0.1", "clean-webpack-plugin": "^4.0.0", "eslint": "^8.54.0", "html-loader": "^5.1.0", "html-webpack-plugin": "^5.6.3", "terser-webpack-plugin": "^5.3.14", "ts-loader": "^9.5.1", "typescript": "^5.3.2", "webpack": "^5.89.0", "webpack-cli": "^5.1.4"}, "eslintConfig": {"extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:@figma/figma-plugins/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"project": "./tsconfig.json"}, "root": true, "rules": {"@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_", "caughtErrorsIgnorePattern": "^_"}]}}}