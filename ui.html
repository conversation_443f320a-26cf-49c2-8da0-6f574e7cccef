<div class="fpw-card-overlay">
  <div class="fpw-card">
    <main class='section fpw-main-section'>
      <nav class='fpw-navigation'>
        <ul class="fpw-flex fpw-navigation-list">
          <li class="fpw-font-base fpw-navigation-item active-tab" data-tab-index="0">API
            Key</li>
          <li class="fpw-font-base fpw-navigation-item" data-tab-index="1">
            Guidelines</li>
          <li class="fpw-font-base fpw-navigation-item" data-tab-index="2">
            Select Layer</li>
          <li class="fpw-font-base fpw-navigation-item" data-tab-index="3">
            Generate</li>
        </ul>
      </nav>
      <div class='fpw-content'>
        <div class="fpw-tab-content fpw-apikey fpw-item-active">
          <div class="fpw-apikey-wrapper">
            <h2 class="fpw-font-base-header fpw-content-title">Figma API Key</h2>

            <form class="fpw-flex fpw-apikey-form">
              <!-- figd_T5hbshP-ygo-QfMhlNLOwNMPmfsFeMeotJll4Nef -->
              <input type="text" id="fpw-figma-api-key" class="fpw-font-base fpw-apikey-pest-field "
                placeholder="Paste your API key" />
            </form>

            <div class="fpw-img-select-loader-wrapper">
              <div class="fpw-generate-load fpw-lowding-box">
                <svg width="60" height="60" viewBox="0 0 50 50">
                  <line x1="25" y1="10" x2="25" y2="15" stroke="#BFBFBF" stroke-width="3" stroke-linecap="round"
                    transform="rotate(0 25 25)">
                    <animate attributeName="opacity" values="0.2;1;0.2" dur="1.2s" begin="0s"
                      repeatCount="indefinite" />
                  </line>
                  <line x1="25" y1="10" x2="25" y2="15" stroke="#BFBFBF" stroke-width="3" stroke-linecap="round"
                    transform="rotate(45 25 25)">
                    <animate attributeName="opacity" values="0.2;1;0.2" dur="1.2s" begin="0.15s"
                      repeatCount="indefinite" />
                  </line>
                  <line x1="25" y1="10" x2="25" y2="15" stroke="#BFBFBF" stroke-width="3" stroke-linecap="round"
                    transform="rotate(90 25 25)">
                    <animate attributeName="opacity" values="0.2;1;0.2" dur="1.2s" begin="0.3s"
                      repeatCount="indefinite" />
                  </line>
                  <line x1="25" y1="10" x2="25" y2="15" stroke="#BFBFBF" stroke-width="3" stroke-linecap="round"
                    transform="rotate(135 25 25)">
                    <animate attributeName="opacity" values="0.2;1;0.2" dur="1.2s" begin="0.45s"
                      repeatCount="indefinite" />
                  </line>
                  <line x1="25" y1="10" x2="25" y2="15" stroke="#BFBFBF" stroke-width="3" stroke-linecap="round"
                    transform="rotate(180 25 25)">
                    <animate attributeName="opacity" values="0.2;1;0.2" dur="1.2s" begin="0.6s"
                      repeatCount="indefinite" />
                  </line>
                  <line x1="25" y1="10" x2="25" y2="15" stroke="#BFBFBF" stroke-width="3" stroke-linecap="round"
                    transform="rotate(225 25 25)">
                    <animate attributeName="opacity" values="0.2;1;0.2" dur="1.2s" begin="0.75s"
                      repeatCount="indefinite" />
                  </line>
                  <line x1="25" y1="10" x2="25" y2="15" stroke="#BFBFBF" stroke-width="3" stroke-linecap="round"
                    transform="rotate(270 25 25)">
                    <animate attributeName="opacity" values="0.2;1;0.2" dur="1.2s" begin="0.9s"
                      repeatCount="indefinite" />
                  </line>
                  <line x1="25" y1="10" x2="25" y2="15" stroke="#BFBFBF" stroke-width="3" stroke-linecap="round"
                    transform="rotate(315 25 25)">
                    <animate attributeName="opacity" values="0.2;1;0.2" dur="1.2s" begin="1.05s"
                      repeatCount="indefinite" />
                  </line>
                </svg>
                <p class="fpw-font-base ">Verifying API Key</p>
              </div>
            </div>

            <p class="fpw-font-base fpw-notification fpw-item-active fpw-generate-notification">
              You can generate your API key from your <a href="#">Figma</a> account
              settings.
            </p>
            <p class="fpw-font-base fpw-notification fpw-item-active fpw-generate-notification">
              <span>&#x1F449;&#x1F3FB;</span> Make sure it's active and has access to the file you're
              working on

            </p>

            <p class="fpw-font-base fpw-notification fpw-error-notification">Oops! That API key doesn’t seem
              to be
              working. Double-check the key and try again — or generate a new one.</p>
          </div>
          <button
            class="fpw-flex fpw-align-items-center fpw-font-base fpw-btn fpw-save-and-continue-button fpw-button-disabled">
            <span>Save and Continue</span>
            <span class="line-height-0">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_76_521)">
                  <path d="M5.8401 3.68002L10.1601 8.00002L5.8401 12.32" stroke="white" stroke-width="1.08"
                    stroke-linecap="round" stroke-linejoin="round" />
                </g>
                <defs>
                  <clipPath id="clip0_76_521">
                    <rect width="16" height="16" fill="white" />
                  </clipPath>
                </defs>
              </svg>
            </span>
          </button>
          <a href="#" class="fpw-font-base fpw-read-more-btn">Read more from our Blog</a>
        </div>

        <div class="fpw-tab-content fpw-guidelines  ">
          <div class="fpw-guidelines-wrapper">
            <h2 class="fpw-font-base-header fpw-content-title">Plugin Usage Guidelines</h2>
            <p class="fpw-font-base fpw-guidelines-description">Follow these essential steps to ensure
              everything works smoothly:</p>
            <ol class="fpw-guidelines-list">
              <li class="fpw-font-base fpw-guidelines-list-item">Use a valid and active Figma API key</li>
              <li class="fpw-font-base fpw-guidelines-list-item">Confirm you have edit access to the file
              </li>
              <li class="fpw-font-base fpw-guidelines-list-item">Use “Auto Layout” wherever possible to
                avoid design breakage.</li>
              <li class="fpw-font-base fpw-guidelines-list-item">Use “Containers” if column layout breaks
                (optional but helpful).</li>
              <li class="fpw-font-base fpw-guidelines-list-item">All Sections & Inner Sections must
                contain <span class="fpw-bold">“Columns”.</span> </li>
              <li class="fpw-font-base fpw-guidelines-list-item">Frames or layers must be named using our
                supported Elementor widget names:</li>
            </ol>

            <ul class="fpw-flex fpw-guideline-layer-name-list ">
              <li class="fpw-font-base fpw-guideline-layer-name-item">Container</li>
              <li class="fpw-font-base fpw-guideline-layer-name-item">Button</li>
              <li class="fpw-font-base fpw-guideline-layer-name-item">Heading</li>
              <li class="fpw-font-base fpw-guideline-layer-name-item">Text Editor</li>
              <li class="fpw-font-base fpw-guideline-layer-name-item">Text Editor</li>
              <li class="fpw-font-base fpw-guideline-layer-name-item">Creative Button</li>
              <li class="fpw-font-base fpw-guideline-layer-name-item">Dual Color Heading</li>
              <li class="fpw-font-base fpw-guideline-layer-name-item">Advanced Menu</li>
              <li class="fpw-font-base fpw-guideline-layer-name-item">Call to Action</li>
              <li class="fpw-font-base fpw-guideline-layer-name-item">Info Box</li>
              <li class="fpw-font-base fpw-guideline-layer-name-item">Post Carousel</li>
              <li class="fpw-font-base fpw-guideline-layer-name-item">Testimonial</li>
              <li class="fpw-font-base fpw-guideline-layer-name-item">Feature List</li>
              <li class="fpw-font-base fpw-guideline-layer-name-item">Counter</li>
              <li class="fpw-font-base fpw-guideline-layer-name-item">Logo Carousel</li>
              <li class="fpw-font-base fpw-guideline-layer-name-item">Logo Carousel</li>
              <li class="fpw-font-base fpw-guideline-layer-name-item">Woo Product List</li>


            </ul>

            <form class="fpw-guidelines-support-btn ">
              <label htmlFor="ISupport" class="fpw-font-base fpw-guidelines-support">
                <input type="checkbox" id="ISupport" />
                <label htmlFor="ISupport" class="support-checkbox"></label>
                I have done them properly
              </label>
            </form>
            <button
              class="fpw-flex fpw-align-items-center fpw-font-base fpw-btn fpw-continue-button fpw-button-disabled">
              <span> Continue</span>
              <span class="line-height-0">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <g clip-path="url(#clip0_76_521)">
                    <path d="M5.8401 3.68002L10.1601 8.00002L5.8401 12.32" stroke="white" stroke-width="1.08"
                      stroke-linecap="round" stroke-linejoin="round" />
                  </g>
                  <defs>
                    <clipPath id="clip0_76_521">
                      <rect width="16" height="16" fill="white" />
                    </clipPath>
                  </defs>
                </svg>
              </span>
            </button>
          </div>
        </div>

        <div class="fpw-tab-content fpw-select-layer">
          <div class="fpw-select-layer-wrapper">
            <div class="fpw-img-select-loader ">
              <div class="fpw-img-select-loader-wrapper">
                <div class="fpw-generate-load fpw-lowding-box">
                  <svg width="60" height="60" viewBox="0 0 50 50">
                    <line x1="25" y1="10" x2="25" y2="15" stroke="#BFBFBF" stroke-width="3" stroke-linecap="round"
                      transform="rotate(0 25 25)">
                      <animate attributeName="opacity" values="0.2;1;0.2" dur="1.2s" begin="0s"
                        repeatCount="indefinite" />
                    </line>
                    <line x1="25" y1="10" x2="25" y2="15" stroke="#BFBFBF" stroke-width="3" stroke-linecap="round"
                      transform="rotate(45 25 25)">
                      <animate attributeName="opacity" values="0.2;1;0.2" dur="1.2s" begin="0.15s"
                        repeatCount="indefinite" />
                    </line>
                    <line x1="25" y1="10" x2="25" y2="15" stroke="#BFBFBF" stroke-width="3" stroke-linecap="round"
                      transform="rotate(90 25 25)">
                      <animate attributeName="opacity" values="0.2;1;0.2" dur="1.2s" begin="0.3s"
                        repeatCount="indefinite" />
                    </line>
                    <line x1="25" y1="10" x2="25" y2="15" stroke="#BFBFBF" stroke-width="3" stroke-linecap="round"
                      transform="rotate(135 25 25)">
                      <animate attributeName="opacity" values="0.2;1;0.2" dur="1.2s" begin="0.45s"
                        repeatCount="indefinite" />
                    </line>
                    <line x1="25" y1="10" x2="25" y2="15" stroke="#BFBFBF" stroke-width="3" stroke-linecap="round"
                      transform="rotate(180 25 25)">
                      <animate attributeName="opacity" values="0.2;1;0.2" dur="1.2s" begin="0.6s"
                        repeatCount="indefinite" />
                    </line>
                    <line x1="25" y1="10" x2="25" y2="15" stroke="#BFBFBF" stroke-width="3" stroke-linecap="round"
                      transform="rotate(225 25 25)">
                      <animate attributeName="opacity" values="0.2;1;0.2" dur="1.2s" begin="0.75s"
                        repeatCount="indefinite" />
                    </line>
                    <line x1="25" y1="10" x2="25" y2="15" stroke="#BFBFBF" stroke-width="3" stroke-linecap="round"
                      transform="rotate(270 25 25)">
                      <animate attributeName="opacity" values="0.2;1;0.2" dur="1.2s" begin="0.9s"
                        repeatCount="indefinite" />
                    </line>
                    <line x1="25" y1="10" x2="25" y2="15" stroke="#BFBFBF" stroke-width="3" stroke-linecap="round"
                      transform="rotate(315 25 25)">
                      <animate attributeName="opacity" values="0.2;1;0.2" dur="1.2s" begin="1.05s"
                        repeatCount="indefinite" />
                    </line>
                  </svg>
                </div>
              </div>
            </div>

            <div class="fpw-after-layer-select">
              <div class="fpw-text-wrapper ">
                <div class="fpw-before-generate-header">
                  <h2 class="fpw-font-base-header fpw-content-title">Select a Layer to Begin</h2>
                  <p class="fpw-font-base fpw-guidelines-description" style="padding: 0 20px;">Select
                    a
                    frame from your Figma file to use with the plugin. Only layers with proper
                    widget
                    names will be processed.</p>
                </div>
              </div>

              <div class='fpw-fig-input-wrapper'>

                <form>
                  <input type="file" id="figFileInput" accept=".fig" class="fpw-fig-input" hidden />
                  <label for="figFileInput" class="fpw-font-base fpw-fig-input-custom-field">
                    Select a Layer
                  </label>

                </form>
              </div>
            </div>

            <div class="fpw-before-layer-select">
              <div class="fpw-after-generate-header">
                <p class="fpw-font-base fpw-guidelines-description">You’ve successfully selected a
                  layer to work with</p>
              </div>
              <div class='fpw-fig-output-custom-field '>
                <img src="" alt="" />
              </div>

              <button
                class="fpw-flex fpw-align-items-center fpw-font-base fpw-btn fpw-generate-button fpw-button-disabled">
                <span>Generate</span>
                <span class="line-height-0">
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g clip-path="url(#clip0_76_521)">
                      <path d="M5.8401 3.68002L10.1601 8.00002L5.8401 12.32" stroke="white" stroke-width="1.08"
                        stroke-linecap="round" stroke-linejoin="round" />
                    </g>
                    <defs>
                      <clipPath id="clip0_76_521">
                        <rect width="16" height="16" fill="white" />
                      </clipPath>
                    </defs>
                  </svg>
                </span>
              </button>
            </div>

          </div>
        </div>

        <div class="fpw-tab-content fpw-generate">
          <div class="fpw-generate-wrapper">
            <div class="fpw-text-wrapper">
              <h2 class="fpw-font-base-header fpw-generate-title">Converting Figma Design to JSON Code
              </h2>
              <p class="fpw-font-base fpw-generate-description">Sit tight! This may take a minute
                depending on the design complexity. <span>&#x1F64F;&#x1F3FB;</span></p>
            </div>
            <!-- generate loader  -->
            <div class="fpw-generate-load-wrapper">
              <div class="fpw-generate-load">
                <svg width="60" height="60" viewBox="0 0 50 50">
                  <line x1="25" y1="10" x2="25" y2="15" stroke="#BFBFBF" stroke-width="3" stroke-linecap="round"
                    transform="rotate(0 25 25)">
                    <animate attributeName="opacity" values="0.2;1;0.2" dur="1.2s" begin="0s"
                      repeatCount="indefinite" />
                  </line>
                  <line x1="25" y1="10" x2="25" y2="15" stroke="#BFBFBF" stroke-width="3" stroke-linecap="round"
                    transform="rotate(45 25 25)">
                    <animate attributeName="opacity" values="0.2;1;0.2" dur="1.2s" begin="0.15s"
                      repeatCount="indefinite" />
                  </line>
                  <line x1="25" y1="10" x2="25" y2="15" stroke="#BFBFBF" stroke-width="3" stroke-linecap="round"
                    transform="rotate(90 25 25)">
                    <animate attributeName="opacity" values="0.2;1;0.2" dur="1.2s" begin="0.3s"
                      repeatCount="indefinite" />
                  </line>
                  <line x1="25" y1="10" x2="25" y2="15" stroke="#BFBFBF" stroke-width="3" stroke-linecap="round"
                    transform="rotate(135 25 25)">
                    <animate attributeName="opacity" values="0.2;1;0.2" dur="1.2s" begin="0.45s"
                      repeatCount="indefinite" />
                  </line>
                  <line x1="25" y1="10" x2="25" y2="15" stroke="#BFBFBF" stroke-width="3" stroke-linecap="round"
                    transform="rotate(180 25 25)">
                    <animate attributeName="opacity" values="0.2;1;0.2" dur="1.2s" begin="0.6s"
                      repeatCount="indefinite" />
                  </line>
                  <line x1="25" y1="10" x2="25" y2="15" stroke="#BFBFBF" stroke-width="3" stroke-linecap="round"
                    transform="rotate(225 25 25)">
                    <animate attributeName="opacity" values="0.2;1;0.2" dur="1.2s" begin="0.75s"
                      repeatCount="indefinite" />
                  </line>
                  <line x1="25" y1="10" x2="25" y2="15" stroke="#BFBFBF" stroke-width="3" stroke-linecap="round"
                    transform="rotate(270 25 25)">
                    <animate attributeName="opacity" values="0.2;1;0.2" dur="1.2s" begin="0.9s"
                      repeatCount="indefinite" />
                  </line>
                  <line x1="25" y1="10" x2="25" y2="15" stroke="#BFBFBF" stroke-width="3" stroke-linecap="round"
                    transform="rotate(315 25 25)">
                    <animate attributeName="opacity" values="0.2;1;0.2" dur="1.2s" begin="1.05s"
                      repeatCount="indefinite" />
                  </line>
                </svg>
              </div>
            </div>
            <button class='fpw-font-base fpw-cancel-btn '>Cancel</button>

          </div>

        </div>

        <div class="fpw-tab-content fpw-complete-concert">

          <div class="fpw-success-wrapper">
            <div class="fpw-text-wrapper">
              <div class="fpw-flex fpw-gap-3  fpw-justify-content-center fpw-align-items-center"
                style="margin-bottom: 8px;">
                <span class="fpw-img-wrapper line-height-0">
                  <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g clip-path="url(#clip0_10_280)">
                      <rect width="14" height="14" rx="7" fill="#3AB325" />
                      <mask id="mask0_10_280" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0"
                        width="14" height="14">
                        <path d="M14 0H0V14H14V0Z" fill="white" />
                      </mask>
                      <g mask="url(#mask0_10_280)">
                        <path
                          d="M7 0C3.14005 0 0 3.14005 0 7C0 10.86 3.14005 14 7 14C10.86 14 14 10.86 14 7C14 3.14005 10.86 0 7 0Z"
                          fill="white" fill-opacity="0.06" />
                        <path
                          d="M10.5479 5.51642L6.75621 9.30802C6.64246 9.42177 6.49313 9.47902 6.34381 9.47902C6.19449 9.47902 6.04517 9.42177 5.93141 9.30802L4.03561 7.41222C3.80746 7.18418 3.80746 6.81546 4.03561 6.58742C4.26366 6.35927 4.63226 6.35927 4.86041 6.58742L6.34381 8.07082L9.72311 4.69162C9.95116 4.46347 10.3198 4.46347 10.5479 4.69162C10.776 4.91967 10.776 5.28827 10.5479 5.51642Z"
                          fill="white" />
                      </g>
                    </g>
                    <defs>
                      <clipPath id="clip0_10_280">
                        <rect width="14" height="14" rx="7" fill="white" />
                      </clipPath>
                    </defs>
                  </svg>


                </span>
                <h2 class="fpw-font-base-header fpw-content-title fpw-margin-bottom-0">Success! You’re
                  All Set
                </h2>
              </div>
              <p class="fpw-font-base fpw-guidelines-description">
                Your layer has been processed successfully.
              </p>
            </div>

            <div class="fpw-generate-load-wrapper">
              <code id="code-block" class="fpw-font-base fpw-generate-load-code">
                              &lt;div&gt;This is a div tag&lt;/div&gt;
                          </code>
            </div>

            <button id="fwp-json-copy" class="fpw-flex fpw-font-base fpw-code-copy-button">
              <span class="line-height-0"><svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                  xmlns="http://www.w3.org/2000/svg">
                  <g clip-path="url(#clip0_76_818)">
                    <path
                      d="M4.66663 6.44466C4.66663 5.97311 4.85395 5.52087 5.18739 5.18743C5.52083 4.85399 5.97307 4.66666 6.44463 4.66666H12.222C12.4554 4.66666 12.6867 4.71265 12.9024 4.80201C13.1181 4.89136 13.3141 5.02233 13.4792 5.18743C13.6443 5.35253 13.7753 5.54854 13.8646 5.76425C13.954 5.97997 14 6.21117 14 6.44466V12.222C14 12.4555 13.954 12.6867 13.8646 12.9024C13.7753 13.1181 13.6443 13.3141 13.4792 13.4792C13.3141 13.6443 13.1181 13.7753 12.9024 13.8647C12.6867 13.954 12.4554 14 12.222 14H6.44463C6.21114 14 5.97993 13.954 5.76421 13.8647C5.5485 13.7753 5.35249 13.6443 5.18739 13.4792C5.02229 13.3141 4.89132 13.1181 4.80197 12.9024C4.71262 12.6867 4.66663 12.4555 4.66663 12.222V6.44466Z"
                      stroke="white" stroke-linecap="round" stroke-linejoin="round" />
                    <path
                      d="M2.67467 11.158C2.47023 11.0415 2.30018 10.873 2.18172 10.6697C2.06325 10.4663 2.00057 10.2353 2 10V3.33333C2 2.6 2.6 2 3.33333 2H10C10.5 2 10.772 2.25667 11 2.66667"
                      stroke="white" stroke-linecap="round" stroke-linejoin="round" />
                  </g>
                  <defs>
                    <clipPath id="clip0_76_818">
                      <rect width="16" height="16" fill="white" />
                    </clipPath>
                  </defs>
                </svg>
              </span>
              <span>Copy Code</span>
            </button>

            <div class="fpw-flex fpw-gap-3">
              <button id="fwp-json-download" class="fpw-flex fpw-align-items-center fpw-font-base fpw-download-button">
                <span class="line-height-0"><svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                    xmlns="http://www.w3.org/2000/svg">
                    <g clip-path="url(#clip0_76_824)">
                      <path
                        d="M2.66663 11.3333V12.6667C2.66663 13.0203 2.8071 13.3594 3.05715 13.6095C3.3072 13.8595 3.64634 14 3.99996 14H12C12.3536 14 12.6927 13.8595 12.9428 13.6095C13.1928 13.3594 13.3333 13.0203 13.3333 12.6667V11.3333"
                        stroke="#F1F1F1" stroke-linecap="round" stroke-linejoin="round" />
                      <path d="M4.66663 7.33333L7.99996 10.6667L11.3333 7.33333" stroke="#F1F1F1" stroke-linecap="round"
                        stroke-linejoin="round" />
                      <path d="M8 2.66666V10.6667" stroke="#F1F1F1" stroke-linecap="round" stroke-linejoin="round" />
                    </g>
                    <defs>
                      <clipPath id="clip0_76_824">
                        <rect width="16" height="16" fill="white" />
                      </clipPath>
                    </defs>
                  </svg>
                </span>
                <span>Download</span>
              </button>

              <button class="fpw-flex fpw-align-items-center fpw-font-base fpw-btn fpw-generate-another-file ">
                <span class="line-height-0">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="icon icon-tabler icons-tabler-outline icon-tabler-reload">
                    <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                    <path d="M19.933 13.041a8 8 0 1 1 -9.925 -8.788c3.899 -1 7.935 1.007 9.425 4.747" />
                    <path d="M20 4v5h-5" />
                  </svg>
                </span>
                <span>Generate Another File</span>

              </button>
            </div>
            <p class="fpw-font-base  fpw-contract-support-team">Got stuck? Please reach out to our <a href="#"> Support
                team</a></p>

          </div>

        </div>
      </div>
    </main>
  </div>
</div>

<style>
  @import url("https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap");

  * {
    margin: 0;
    padding: 0;
    text-decoration: none;
    list-style: none;
    box-sizing: border-box;
  }

  img {
    width: 100%;
    height: auto;
  }

  label,
  span,
  a {
    display: inline-block;
  }

  .fpw-font-base-header {
    font-size: 14px;
    font-weight: 500;
    font-family: "inter", sans-serif;
    color: #ECECEC;
  }

  .fpw-font-base {
    font-size: 12px;
    font-weight: 500;
    font-family: "inter", sans-serif;
    color: #A7A7A7;
  }

  .fpw-bold {
    font-weight: 600;
  }

  .line-height-0 {
    line-height: 0;
  }

  .fpw-margin-bottom-0 {
    margin-bottom: 0 !important;
  }

  .fpw-tab-content {
    display: none;
    padding-bottom: 1px;
  }

  .fpw-item-inactive {
    display: none;
  }

  .fpw-notification {
    display: none;
    ;
  }

  .fpw-item-active {
    display: block !important;
  }

  .fpw-item-forcely-inactive {
    display: none !important;
  }


  .fp-container {
    max-width: 340px;
    margin: 0 auto;
  }

  .fpw-card-overlay {
    height: 100%;
    width: 100vw;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 999;
    overflow-y: auto;
    padding: 0px;
    background-color: var(--background-primary-color);
    border: 1px solid rgba(255, 255, 255, 0.1019607843);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .fpw-card {
    box-shadow: 2px 2px 6px 0px rgba(0, 0, 0, 0.2);
    background-color: var(--background-primary-color);
    max-width: 100%;
    width: 100%;
    /* border-radius: 0px; */
    /* max-height: 503px; */
    height: 100%;
  }


  .fpw-flex {
    display: flex;
  }

  .fpw-gap-3 {
    gap: 5px;
  }

  .fpw-flex-column {
    flex-direction: column;
  }

  .fpw-align-items-center {
    align-items: center;
  }

  .fpw-justify-content-center {
    justify-content: center;
  }

  .fpw-justify-content-space-between {
    justify-content: space-between;
  }

  .fpw-btn {
    width: 100%;
    padding: 12.5px 5px;
    cursor: pointer;
    background-color: #007CE2;
    color: var(--primary-text-color);
    border-radius: 8px;
    border: none;
    outline: none;
    gap: 4px;
    justify-content: center;
    margin-bottom: 24px;
    z-index: 999;
  }

  .fpw-cancel-btn {
    text-align: center;
    color: #C3C3C3;
    background-color: transparent;
    border: none;
    margin: 0 auto;
    cursor: pointer;
    padding: 10px;
    width: 100%;
  }

  .fpw-generate-another-file {
    margin-bottom: 0px;
  }

  :root {
    --background-primary-color: #2C2C2C;
    --background-secendary-color: #383838;
    --primary-text-color: #FFFFFF;
    --secendary-text-color: #878787;
    --border-color: #404040;
    --text-color-1: #A7A7A7;
    --text-color-2: #C3C3C3;
    --text-color-3: #ECECEC;
  }


  .fpw-main-section .fpw-navigation {
    padding: 8px 16px;
    border-bottom: 1px solid var(--border-color);
  }

  .fpw-main-section .fpw-navigation-item {
    padding: 4px 8px;
    border-radius: 4px;
    margin-left: 4px;
    color: var(--secendary-text-color);
  }

  .fpw-main-section .fpw-navigation-item:first-child {
    margin-left: 0;
  }

  .fpw-main-section .fpw-navigation-item.active-tab {
    color: var(--primary-text-color);
    background-color: var(--background-secendary-color);
  }

  .fpw-main-section .fpw-content {
    padding: 16px 16px 0px 16px;
  }

  .fpw-main-section .fpw-content-title {
    color: var(--primary-text-color);
    margin-bottom: 8px;
  }

  .fpw-main-section .fpw-apikey-wrapper {
    margin-bottom: 24px;
  }

  .fpw-main-section .fpw-apikey-wrapper .fpw-generate-load {
    text-align: center;
    display: none;
  }

  .fpw-content .fpw-read-more-btn {
    text-align: center;
    cursor: pointer;
    color: var(--text-color-2);
    width: 100%;
    /* text-align: center; */
  }

  .fpw-main-section .fpw-error-notification {
    color: #FD6A6A;
    padding-right: 30px;
    display: none;
  }

  .fpw-main-section .fpw-apikey-form {
    border: 1px solid #444444;
    background: #383838;
    border-radius: 4px;
    padding: 6px 2px;
    margin-bottom: 8px;
    position: relative;
    overflow: hidden;
  }

  .fpw-main-section .fpw-apikey-form.fpw-error-field {
    border: 1px solid #FB1313;
    border-radius: 4px;
  }

  .fpw-main-section .fpw-apikey-form::before {
    content: '';
    position: absolute;
    width: 10px;
    right: 0;
    top: 0;
    bottom: 0;
    height: 100%;
    background-color: #383838;
    box-shadow: -5px 0 4px 0px #383838;
  }

  .fpw-main-section .fpw-apikey-pest-field {
    background-color: transparent;
    border: none;
    outline: none;
    width: 100%;
    padding: 2px 6px;
    color: var(--primary-text-color);

  }


  .fpw-main-section .fpw-apikey-pest-field::-moz-placeholder {
    color: #888888;
  }

  .fpw-main-section .fpw-apikey-pest-field::placeholder {
    color: #888888;
  }

  .fpw-main-section .fpw-generate-notification {
    color: var(--text-color-1);
    line-height: 1.5;
  }

  .fpw-main-section .fpw-generate .fpw-generate-wrapper .fpw-text-wrapper .fpw-generate-title {
    text-align: center;
    margin-bottom: 8px;
  }

  .fpw-main-section .fpw-generate .fpw-generate-wrapper .fpw-text-wrapper .fpw-generate-description {
    text-align: center;
    margin-bottom: 8px;
    margin-bottom: 16px;
  }

  .fpw-main-section .fpw-generate-notification a {
    display: inline;
    color: #1294FF;
    text-decoration: underline;
  }

  .fpw-main-section .fpw-content .fpw-guidelines-wrapper .fpw-guideline-layer-name-list {
    gap: 4px;
    flex-wrap: wrap;
    padding-right: 20px;
    margin-bottom: 32px;
  }

  .fpw-main-section .fpw-content .fpw-guidelines-wrapper .fpw-guidelines-support {
    display: flex;
    align-items: center;
    gap: 6px;
    font-weight: 400;
    margin-bottom: 16px;
    max-width: 200px;
    cursor: pointer;
  }

  .fpw-main-section .fpw-content .fpw-guidelines-wrapper input[type=checkbox] {
    display: none;
  }

  .fpw-main-section .fpw-content .fpw-guidelines-wrapper .support-checkbox {
    width: 14px;
    height: 14px;
    border: 1px solid #4F4F4F;
    overflow: hidden;
    border-radius: 2px;
    cursor: pointer;
    position: relative;
    z-index: -1;
  }

  .fpw-main-section .fpw-guidelines-support input[type=checkbox]:checked+.support-checkbox {
    background-color: #007CE2;
  }

  .fpw-main-section .fpw-guidelines-support input[type=checkbox]:checked+.support-checkbox::after {
    content: "";
    display: block;
    position: absolute;
    left: 1px;
    top: 1px;
    width: 10px;
    height: 10px;
    background-image: url("data:image/svg+xml,%3Csvg width='8' height='6' viewBox='0 0 8 6' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M1.375 3.375L2.875 4.875L6.625 1.125' stroke='white' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
  }


  .fpw-main-section .fpw-content .fpw-guidelines-wrapper .fpw-guideline-layer-name-list .fpw-guideline-layer-name-item {
    color: #ECECEC;
    padding: 4px;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.04);
  }

  .fpw-main-section .fpw-content .fpw-guidelines-wrapper .fpw-guidelines-description {
    margin-bottom: 16px;
  }

  .fpw-main-section .fpw-content .fpw-guidelines-wrapper .fpw-guidelines-list {
    list-style: auto;
    padding-left: 14px;
    padding-right: 35px;
    margin-bottom: 24px;
  }

  .fpw-main-section .fpw-content .fpw-guidelines-wrapper .fpw-guidelines-list .fpw-guidelines-list-item {
    list-style: auto;
    color: var(--text-color-3);
    margin-bottom: 12px;
    font-weight: 400;
    line-height: 1.2;
  }

  .fpw-main-section .fpw-content .fpw-guidelines-wrapper .fpw-guidelines-list .fpw-guidelines-list-item:last-child {
    margin-bottom: 0;
  }

  .fpw-main-section .fpw-content .fpw-select-layer-wrapper .fpw-img-select-loader .fpw-img-select-loader-wrapper {
    display: none;

  }

  .fpw-main-section .fpw-content .fpw-select-layer-wrapper .fpw-img-select-loader .fpw-lowding-box {
    display: flex;
    justify-content: center;
    align-items: center;
    height: auto;
    width: 100%;
    background-color: transparent;
  }

  .fpw-main-section .fpw-content .fpw-select-layer-wrapper .fpw-img-wrapper {
    width: auto;
    height: auto;
    text-align: center;
    margin-bottom: 0px;
  }

  .fpw-main-section .fpw-content .fpw-select-layer-wrapper .fpw-img-wrapper img {
    width: auto;
    height: 72px;
    -o-object-fit: cover;
    object-fit: cover;
  }

  .fpw-main-section .fpw-content .fpw-select-layer-wrapper .fpw-text-wrapper {
    margin-bottom: 16px;
    text-align: center;
  }

  .fpw-main-section .fpw-content .fpw-select-layer-wrapper .fpw-fig-input-wrapper {
    margin-bottom: 24px;
  }

  .fpw-main-section .fpw-content .fpw-select-layer-wrapper .fpw-fig-input-wrapper .fpw-fig-input {
    display: none;
  }

  .fpw-main-section .fpw-content .fpw-select-layer-wrapper .fpw-fig-input-wrapper .fpw-fig-input-custom-field {
    width: 100%;
    text-align: center;
    padding: 24px;
    border: 1px dashed #444444;
    border-radius: 5px;
    color: var(--text-color-3);
    cursor: pointer;
  }

  .fpw-main-section .fpw-content .fpw-before-layer-select {
    display: none;
  }

  .fpw-main-section .fpw-content .fpw-before-layer-select .fpw-guidelines-description {
    text-align: center;
    margin-bottom: 16px;
  }

  .fpw-main-section .fpw-content .fpw-before-layer-select .fpw-fig-output-custom-field {
    width: 100%;
    text-align: center;
    border: 1px dashed #444444;
    border-radius: 5px;
    color: var(--text-color-3);
    cursor: pointer;
    height: 180px;
    margin-bottom: 24px;
  }

  .fpw-main-section .fpw-content .fpw-before-layer-select .fpw-fig-output-custom-field img {
    height: 100%;
    width: auto;
    max-width: 100%;
    -o-object-fit: cover;
    object-fit: cover;
  }

  /* .fpw-main-section .fpw-content  .fpw-after-layer-select{
		display: none;
	} */

  .fpw-main-section .fpw-content .fpw-after-layer-select .fpw-text-wrapper {
    margin-bottom: 16px;
    text-align: center;
  }

  .fpw-main-section .fpw-content .fpw-generate-load-wrapper {
    border: 1px solid #444444;
    background-color: var(--background-secendary-color);
    border-radius: 8px;
    height: 164px;
    width: 100%;
    display: none;
    /* background-color: red; */
    display: block;
    margin-bottom: 16px;
  }



  .fpw-main-section .fpw-content .fpw-generate-load-wrapper .fpw-generate-load {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
  }

  .fpw-main-section .fpw-content .fpw-success-wrapper .fpw-img-wrapper {
    width: auto;
    height: auto;
    text-align: center;
    margin-bottom: 0px;
  }

  .fpw-main-section .fpw-content .fpw-success-wrapper .fpw-contract-support-team {
    font-size: 10px;
    text-align: center;
    padding: 16px;
  }

  .fpw-main-section .fpw-content .fpw-success-wrapper .fpw-contract-support-team a {
    color: rgba(0, 124, 226, 1);
    cursor: pointer;
    font-weight: 500;
    text-decoration: underline;
  }

  .fpw-main-section .fpw-content .fpw-success-wrapper .fpw-img-wrapper img {
    width: auto;
    height: 40px;
    -o-object-fit: cover;
    object-fit: cover;
  }

  .fpw-main-section .fpw-content .fpw-success-wrapper .fpw-text-wrapper {
    margin-bottom: 16px;
    text-align: center;
  }

  .fpw-main-section .fpw-content .fpw-success-wrapper .fpw-generate-load-wrapper {
    width: 100%;
  }

  .fpw-main-section .fpw-content .fpw-success-wrapper .fpw-generate-load-wrapper .fpw-generate-load-code {
    display: block;
    border: 1px solid #444444;
    background-color: var(--background-secendary-color);
    border-radius: 8px;
    height: 164px;
    width: 100%;
    overflow-y: auto;
    margin-bottom: 16px;
    padding: 12px 12px 0 12px;

  }

  .fpw-main-section .fpw-content .fpw-success-wrapper .fpw-generate-load-wrapper .fpw-generate-load-code::-webkit-scrollbar {
    width: 8px;
    background-color: #515151;
    overflow: hidden;
    border-top-right-radius: 7px;
    border-bottom-right-radius: 7px;
  }

  .fpw-main-section .fpw-content .fpw-success-wrapper .fpw-generate-load-wrapper .fpw-generate-load-code::-webkit-scrollbar-thumb {
    background-color: #7C7C7C;
    /* Scrollbar thumb */
    border-radius: 2px;
  }

  .fpw-main-section .fpw-content .fpw-success-wrapper .fpw-download-button,
  .fpw-main-section .fpw-content .fpw-success-wrapper .fpw-code-copy-button {
    width: 100%;
    padding: 12px;
    cursor: pointer;
    background-color: #007CE2;
    color: var(--primary-text-color);
    border-radius: 8px;
    border: none;
    outline: none;
    gap: 4px;
    justify-content: center;
    margin-bottom: 16px;
    z-index: 999;
  }

  .fpw-main-section .fpw-content .fpw-success-wrapper .fpw-download-button,
  .fpw-generate-another-file {
    background-color: transparent;
    border: 1px solid #404040;
    color: #F1F1F1;
    padding: 12.5px 5px;
    margin-bottom: 0px;
  }

  .fpw-main-section .fpw-continue-button-wrapper {
    padding: 8px 16px;
    text-align: center;
  }


  .fpw-main-section .fpw-continue-button-wrapper .fpw-read-more-btn {
    text-align: center;
    cursor: pointer;
    color: var(--text-color-2);

  }



  .fpw-button-disabled {
    cursor: not-allowed;
    pointer-events: none !important;
    background-color: #007ce269 !important;
  }

  /*# sourceMappingURL=style.css.map */
</style>

<script>
  // Variable declarations at top
  let activeTab = 0;
  const fpwNavigationItems = document.querySelectorAll('.fpw-navigation-item');
  const fwpTabContent = document.querySelectorAll('.fpw-tab-content');
  const apiKeyInput = document.querySelector('.fpw-apikey-pest-field');
  const apiKeyFildError = document.querySelector('.fpw-apikey-form');
  const apiKeyTextError = document.querySelector('.fpw-error-notification');
  const generateNotifications = document.querySelectorAll('.fpw-generate-notification');
  const guidelinesCheckbox = document.querySelector('.fpw-guidelines-support input[type=checkbox]');
  const saveButton = document.querySelector('.fpw-save-and-continue-button');
  const continueButton = document.querySelector('.fpw-continue-button');
  const generateButton = document.querySelector('.fpw-generate-button');
  const figOutputField = document.querySelector('.fpw-fig-output-custom-field');
  const afterLayerSelect = document.querySelector('.fpw-after-layer-select');
  const beforeLayerSelect = document.querySelector('.fpw-before-layer-select');
  const copyButton = document.querySelector('#fwp-json-copy');
  const downloadButton = document.querySelector('#fwp-json-download');
  const generateAnotherFileButton = document.querySelector('.fpw-generate-another-file');
  const cancelButton = document.querySelector('.fpw-cancel-btn');
  const notificationControllers = document.querySelectorAll('.fpw-notification');
  let isProcessCanceled = false;


  // Initialization
  setActiveTab(activeTab);
  handleApiKeyInputChange();
  guidelinesChecked();

  // Event listeners grouped together
  fpwNavigationItems.forEach(tab => {
    tab.addEventListener('click', function () {
      const tabIndex = parseInt(this.getAttribute('data-tab-index'));
      if (tabIndex < activeTab) {
        activeTab = tabIndex;
        setActiveTab(activeTab);
      }
    });
  });

  if (saveButton) saveButton.addEventListener("click", (e) => {
    handleApiKeySave(e);
    removeNotification()
    document.querySelector(' .fpw-apikey-wrapper .fpw-generate-load').classList.add('fpw-item-active');
  });
  if (continueButton) continueButton.addEventListener("click", handle3rdTab);
  if (generateButton) generateButton.addEventListener("click", () => {
    isProcessCanceled = false;
    document.querySelector('.fpw-complete-concert').classList.remove('fpw-item-forcely-inactive');
    handle4thTab();
  });

  if (generateAnotherFileButton) generateAnotherFileButton.addEventListener('click', () => {
    activeTab = 2;
    setActiveTab(activeTab);
  });

  apiKeyInput.addEventListener('input', handleApiKeyInputChange);
  guidelinesCheckbox.addEventListener('change', guidelinesChecked);

  // Functions grouped by category

  // Tab management functions
  function setActiveTab(tabIndex) {
    fpwNavigationItems.forEach(item => item.classList.remove('active-tab'));
    if (fpwNavigationItems[tabIndex]) fpwNavigationItems[tabIndex].classList.add('active-tab');
    setActiveTabContents(tabIndex);
  }

  function setActiveTabContents(setActiveTabContent) {
    if (setActiveTabContent === 0) apikeyFild()
    else if (setActiveTabContent === 1) guidelinesFild()
    else if (setActiveTabContent === 2) selectField()
    else if (setActiveTabContent === 3) completeConcertFild()
    else apikeyFild()
  }

  function removeActiveTabCongtent() {
    fwpTabContent.forEach(item => item.classList.remove('fpw-item-active'));
  }

  // Tab content field functions
  function apikeyFild() {
    removeActiveTabCongtent()
    document.querySelector('.fpw-apikey').classList.add('fpw-item-active');
  }

  function guidelinesFild() {
    removeActiveTabCongtent()
    document.querySelector('.fpw-guidelines').classList.add('fpw-item-active');
    document.querySelector('.fpw-save-and-continue-button').innerHTML = 'Update and Continue';
  }

  function selectField() {
    removeActiveTabCongtent()
    document.querySelector('.fpw-select-layer').classList.add('fpw-item-active');

    parent.postMessage({
      pluginMessage: {
        type: 'select-layer'
      }
    }, '*');

  }

  function completeConcertFild() {
    removeActiveTabCongtent()
    document.querySelector('.fpw-generate').classList.add('fpw-item-active');

    parent.postMessage({
      pluginMessage: {
        type: 'generate-json'
      }
    }, '*');

  }

  // Button handlers
  function handle3rdTab() {
    isProcessCanceled = false;
    if (activeTab < 2) {
      activeTab = 2;
      setActiveTab(activeTab);
    }
  }

  function handle4thTab() {
    if (activeTab < 3) {
      activeTab = 3;
      setActiveTab(activeTab);
    }
  }

  // API Key functions
  function handleApiKeyInputChange() {
    if (apiKeyInput.value.trim() !== '') {
      saveButton.classList.remove('fpw-button-disabled');
    } else {
      saveButton.classList.add('fpw-button-disabled');
    }
  }

  function handleApiKeySave() {
    const apiKey = apiKeyInput.value.trim();
    if (apiKey) {
      parent.postMessage({
        pluginMessage: {
          type: 'save-api-key',
          figmaApiKey: apiKey
        }
      }, '*');
    }
  }

  // Guidelines checkbox function
  function guidelinesChecked() {
    if (guidelinesCheckbox.checked) {
      continueButton.classList.remove('fpw-button-disabled');
    } else {
      continueButton.classList.add('fpw-button-disabled');
    }
  }

  if (cancelButton) {
    cancelButton.addEventListener('click', () => {
      isProcessCanceled = true;
      // Simply set active tab to 2 without activating any content sections
      activeTab = 2;
      setActiveTab(activeTab);
    });
  }

  // Clipboard and Download Functions
  const copyToClipboard = async (text) => {
    try {
      if (navigator.clipboard && navigator.clipboard.writeText) {
        await navigator.clipboard.writeText(text);
        notifyMessage('JSON copied to clipboard.');
      } else {
        fallbackCopyToClipboard(text);
      }
    } catch (err) {

      fallbackCopyToClipboard(text);
    }
  };

  const fallbackCopyToClipboard = (text) => {
    const textarea = document.createElement('textarea');
    textarea.value = text;
    document.body.appendChild(textarea);
    textarea.select();

    try {
      const successful = document.execCommand('copy');
      if (successful) {
        notifyMessage('JSON copied to clipboard.');
      }
    } catch (err) {

    }

    document.body.removeChild(textarea);
  };

  const downloadJson = (jsonOutput) => {
    if (!jsonOutput) {
      alert('Failed to download JSON.');
      return;
    }

    const blob = new Blob([jsonOutput], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const anchor = document.createElement('a');

    anchor.href = url;
    // generate dynamic name from main frame name with timestamp to make it unique
    let fileName = `figma-to-wp-for-elementor-${new Date().getTime()}.json`;

    const json = JSON.parse(jsonOutput);
    if (json.title) {
      fileName = json.title;
    }

    anchor.download = fileName;
    anchor.click();

    URL.revokeObjectURL(url);
  };

  const notifyMessage = (msg) => {
    parent.postMessage({
      pluginMessage: {
        type: 'notify-message',
        message: msg
      }
    }, '*');
  };


  function removeNotification() {
    notificationControllers.forEach(controller => {
      controller.classList.remove('fpw-item-active');
    });
  }


  // Message handler
  onmessage = (event) => {
    const msg = event.data.pluginMessage;

    if (msg.type === 'load-settings' && msg.figmaApiKey) {
      apiKeyInput.value = msg.figmaApiKey;

      handleApiKeyInputChange();
      if (activeTab < 1) {
        activeTab = 1;
        setActiveTab(activeTab);
      }
    }

    if (msg === 'api-key-saved') {
      document.querySelector('.fpw-apikey').classList.remove('fpw-item-active');
      document.querySelector('.fpw-guidelines').classList.add('fpw-item-active');
      activeTab = 1;
      setActiveTab(activeTab);
    }

    if (msg.type === 'api-key-error') {
      apiKeyFildError.classList.add('fpw-error-field');
      apiKeyTextError.classList.add('fpw-item-active');
      document.querySelector(' .fpw-apikey-wrapper .fpw-generate-load').classList.remove('fpw-item-active');

      generateNotifications.forEach((generateNotification) => {
        generateNotification.classList.add('fpw-item-inactive');
      });
    }

    if (msg.type === 'api-key-success') {
      apiKeyFildError.classList.remove('fpw-error-field');
      apiKeyTextError.classList.remove('fpw-item-active');
      document.querySelector(' .fpw-apikey-wrapper .fpw-generate-load').classList.remove('fpw-item-active');

      generateNotifications.forEach((generateNotification) => {
        generateNotification.classList.remove('fpw-item-inactive');
        generateNotification.classList.add('fpw-item-active');
      });

      if (activeTab < 1) {
        activeTab = 1;
        setActiveTab(activeTab);
      }
    }

    if (msg.type === 'layer-selected' && msg.imageUrl && figOutputField) {

      // Update the image source
      const imgElement = figOutputField.querySelector('img');
      const loaderWrapper = document.querySelector('.fpw-img-select-loader-wrapper');

      if (imgElement) {
        if (msg.imageUrl) {
          // Valid image case
          imgElement.src = msg.imageUrl;
          imgElement.alt = msg.data || '';
          afterLayerSelect.classList.add('fpw-item-inactive');
          beforeLayerSelect.classList.add('fpw-item-active');

          // Hide loader and enable button
          if (loaderWrapper) loaderWrapper.classList.remove('fpw-item-active');
          generateButton.classList.remove('fpw-button-disabled');
        } else {
          // Empty/error case
          imgElement.src = '';
          imgElement.alt = '';

          // Show loader and disable button
          if (loaderWrapper) loaderWrapper.classList.add('fpw-item-active');
          generateButton.classList.add('fpw-button-disabled');
        }
      } else {
        // No image element found - show loader as fallback
        if (loaderWrapper) loaderWrapper.classList.add('fpw-item-active');
        generateButton.classList.add('fpw-button-disabled');
      }
    } else {
      // No imageUrl or figOutputField - ensure loading state
      const imgElement = figOutputField.querySelector('img');
      if (imgElement && imgElement.src === '') {
        const loaderWrapper = document.querySelector('.fpw-img-select-loader-wrapper');
        if (loaderWrapper) loaderWrapper.classList.add('fpw-item-active');
        if (generateButton) generateButton.classList.add('fpw-button-disabled');
      }
    }

    if (msg.type === 'jsonData' && isProcessCanceled === false) {


      if (!msg.data) {

        return;
      }

      if (typeof msg.data === 'object') {
        const jsonOutput = JSON.stringify(msg.data, null, 2);

        // Display in UI
        const codeElement = document.querySelector('#code-block');

        if (codeElement) {
          codeElement.textContent = jsonOutput;
        }

        document.querySelector('.fpw-generate').classList.remove('fpw-item-active');
        document.querySelector('.fpw-complete-concert').classList.add('fpw-item-active');

        document.querySelector('#fwp-json-copy').onclick = () => {
          copyToClipboard(jsonOutput);
        }

        document.querySelector('#fwp-json-download').onclick = () => {
          downloadJson(jsonOutput);
        }
      } else {
      }
    }
  }
</script>